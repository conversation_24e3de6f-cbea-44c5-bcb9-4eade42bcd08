import { SelectableCell } from "./selector-config"

export const bonusSelectors: SelectableCell[] = [
  {
    cell_id: "bonus-1",
    chips_placed: [],
    cell_selectors: ["201"],
    isSelectable: true,
    bonusSource: "/assets/images/bonuses/lucky-pot.webp",
    bonusOdds: "200-1",
    bet_type_id: 22,
  },
  {
    cell_id: "bonus-2",
    chips_placed: [],
    cell_selectors: ["202"],
    isSelectable: true,
    bonusSource: "/assets/images/bonuses/ying-yang.webp",
    bonusOdds: "20-1",
    bet_type_id: 23,
  },
  {
    cell_id: "bonus-3",
    chips_placed: [],
    cell_selectors: ["203"],
    isSelectable: true,
    bonusSource: "/assets/images/bonuses/treasure-chest.webp",
    bonusOdds: "3-1",
    bet_type_id: 24,
  },
  {
    cell_id: "bonus-4",
    chips_placed: [],
    cell_selectors: ["204"],
    isSelectable: true,
    bonusSource: "/assets/images/bonuses/chinese-fan.webp",
    bonusOdds: "1-1",
    bet_type_id: 25,
  },
  {
    cell_id: "bonus-5",
    chips_placed: [],
    cell_selectors: ["205"],
    isSelectable: true,
    bonusSource: "/assets/images/bonuses/lantern.webp",
    bonusOdds: "3-1",
    bet_type_id: 26,
  },
  {
    cell_id: "bonus-6",
    chips_placed: [],
    cell_selectors: ["207"],
    isSelectable: true,
    bonusSource: "/assets/images/bonuses/chinese-coin.webp",
    bonusOdds: "20-1",
    bet_type_id: 27,
  },
  {
    cell_id: "bonus-7",
    chips_placed: [],
    cell_selectors: ["208"],
    isSelectable: true,
    bonusSource: "/assets/images/bonuses/gold-bag.webp",
    bonusOdds: "200-1",
    bet_type_id: 28,
  },
]
