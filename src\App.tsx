import { AnimatePresence } from "motion/react"
import { Suspense, useCallback, useEffect, lazy } from "react"
import { Route, BrowserRouter as Router, Routes } from "react-router-dom"

import { soundMap } from "@config/audio-config"
import VideoPlayer from "./features/streaming/video-player"
import { fetchCurrencyCode, useAuth, useSignalR } from "./hooks"
import {
  EnhancedErrorBoundary,
  QueryProvider,
  logger,
  errorReporting,
  ResponsiveLayoutProvider,
  GameEventsProvider,
} from "./middleware"
import { initializeStorageHealthMonitoring } from "./lib/logger"
import { useBettingStore } from "./stores/betting-store"
import { useGameStateStore } from "./stores/game-state-store"
import { useSettingsStore } from "./stores/settings-store"
import { tryCatch } from "@maxmorozoff/try-catch-tuple"

// Lazy load the main views
const Online = lazy(() => import("./layouts/Online"))
const Retail = lazy(() => import("./layouts/Retail"))
const LoadingFallback = lazy(() =>
  import("./components/common/loading-fallback").then((module) => ({
    default: module.LoadingFallback,
  }))
)

function App() {
  useSignalR()

  const { token } = useAuth()
  const { setCurrencyCode } = useGameStateStore()
  const preloadSounds = useSettingsStore((state) => state.preloadSounds)
  const initializeBetTypes = useBettingStore(
    (state) => state.initializeBetTypes
  )

  useEffect(() => {
    errorReporting.initialize()

    // Initialize storage health monitoring to prevent localStorage quota issues
    initializeStorageHealthMonitoring()

    return () => {
      errorReporting.shutdown()
    }
  }, [])

  useEffect(() => {
    logger.info("Initializing application", { context: "App" })
    preloadSounds(soundMap)

    const initializeWithRetry = async () => {
      const [betTypes, error] = await tryCatch(
        () => initializeBetTypes(),
        "Initialize Bet Types"
      )
      if (error) {
        logger.error("Failed to initialize bet types", error, {
          context: "App",
        })
        return
      }
      if (betTypes) {
        logger.info("Bet types initialized successfully", { context: "App" })
        return
      }
    }

    initializeWithRetry()
  }, [initializeBetTypes, preloadSounds])

  const renderBuild = useCallback(() => {
    switch (import.meta.env.VITE_APP_BUILD) {
      case "online":
        return <Online />
      case "retail":
        return <Retail />
      case "video":
        return <VideoPlayer />
      default:
        return <Online />
    }
  }, [])

  useEffect(() => {
    const fetchCurrency = async () => {
      const currencyCode = await fetchCurrencyCode(token)
      if (currencyCode) {
        setCurrencyCode(currencyCode)
      }
    }

    fetchCurrency()
  }, [token, setCurrencyCode])

  // Cleanup audio resources on app unmount to prevent WebMediaPlayer limit exceeded
  useEffect(() => {
    const cleanupAudioResources =
      useSettingsStore.getState().cleanupAudioResources

    return () => {
      cleanupAudioResources()
    }
  }, [])

  return (
    <QueryProvider>
      <GameEventsProvider>
        <ResponsiveLayoutProvider>
          <EnhancedErrorBoundary context='App'>
            <AnimatePresence mode='popLayout'>
              <Router>
                <Suspense fallback={<LoadingFallback />}>
                  <Routes>
                    <Route path='/' element={renderBuild()} />
                    {import.meta.env.VITE_APP_BUILD === "retail" && (
                      <Route path='/video' element={<VideoPlayer />} />
                    )}
                  </Routes>
                </Suspense>
              </Router>
            </AnimatePresence>
          </EnhancedErrorBoundary>
        </ResponsiveLayoutProvider>
      </GameEventsProvider>
    </QueryProvider>
  )
}

export default App
