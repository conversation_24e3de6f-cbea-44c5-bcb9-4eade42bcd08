import type React from "react"
import { ChipStack } from "@/components/common/chip-stack"
import { toast, useRoundPhase } from "@/hooks"
import { cn } from "@/lib/utils"
import { useBettingStore } from "@/stores/betting-store"
import type { SelectableCell } from "@config/selector-config"

// Define style constants for reuse
const STYLES = {
  // Base container styling
  container:
    "relative flex flex-col items-center justify-center cursor-pointer rounded-md hover:brightness-90 transition-all min-h-0 min-w-0",

  // Image container - using CSS variable for dynamic sizing
  imageContainer:
    "aspect-square flex items-center justify-center min-h-0 min-w-0",
  image: "w-full h-full object-contain max-h-full",

  // Text styling - smaller on small screens
  payoutText:
    "text-white font-semibold text-center leading-none text-[clamp(10px,0.2em,14px)] lg:text-[clamp(12px,0.2em,16px)]",

  // Chip positioning
  chipPosition:
    "absolute top-3 right-3 transform translate-x-1/4 -translate-y-1/4 scale-[0.65]",
}

interface BonusSymbolProps {
  bonus: SelectableCell
  className?: string
}

/**
 * Component for displaying a single bonus bet symbol with payout ratio
 */
const BonusSymbol: React.FC<BonusSymbolProps> = ({ bonus, className = "" }) => {
  const addChip = useBettingStore((state) => state.addChip)
  const selectedChip = useBettingStore((state) => state.selectedChip)
  const placedChips = useBettingStore((state) => state.placedChips)
  const { Betting } = useRoundPhase()

  // Get the actual placed chips for this cell from the store
  const placedCell = placedChips.find((cell) => cell.cell_id === bonus.cell_id)
  const hasChipsPlaced = placedCell && placedCell.chips_placed.length > 0

  const handleClick = () => {
    if (Betting && bonus.isSelectable) {
      addChip(bonus, selectedChip)
    }
  }

  return (
    <div className={cn(STYLES.container, className)} onClick={handleClick}>
      {/* Bonus symbol image */}
      <div
        className={STYLES.imageContainer}
        style={{ width: "var(--symbol-size, 35px)" }}
      >
        <img
          src={bonus.bonusSource || "/placeholder.svg"}
          alt={`Bonus symbol ${bonus.cell_id}`}
          className={STYLES.image}
        />
      </div>

      {/* Payout ratio */}
      <div className={STYLES.payoutText}>{bonus.bonusOdds}</div>

      {/* Chip stack if chips are placed - now using actual store data */}
      {hasChipsPlaced && (
        <div className={STYLES.chipPosition}>
          <ChipStack cellId={bonus.cell_id} />
        </div>
      )}
    </div>
  )
}

export default BonusSymbol
