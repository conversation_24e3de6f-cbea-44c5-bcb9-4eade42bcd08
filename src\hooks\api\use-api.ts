import { apiClient, authMiddleware, logger, toastService } from "@/middleware"
import { SelectableCell } from "@config/selector-config"

// Import middleware

// Define History type locally to avoid dependency on top-nav
export interface History {
  rouletteNumber: number
  bonusNumber: number
}

export interface RouletteBetRequest {
  playerGuid: number
  currencyCode: string
  rouletteDrawId: number
  bets: Partial<SelectableCell>[]
}

export interface BetTypes {
  betTypeId: number
  betTypeName: string
  odds: number
  betGroupId: number
}

export interface PlayerBetHistory {
  betTypeId: number
  betTypeName: string
  stake: number
  potentialPayout: number
  betNumbers: string
  cellId: string
  rouletteDrawId: number
  message: string | null
}

export interface ApiResponse<T> {
  data?: T
  code?: number
  message?: string
  betSlipId?: number
}

export interface CurrencyResponse {
  response: {
    currencySymbol: string
  }
}

export type GraphData = {
  number: number
  value: number
}[]

export interface BetHistoryNav {
  date: string // Format: "2025-03-03T00:00:00"
  game: string | null
  bet: number
  winLose: number
  timeStamp: string | null // Format: "09:27:17" or null
}

// API endpoints
const ENDPOINTS = {
  BALANCE: import.meta.env.VITE_APP_BALANCE,
  SUBMIT_BET: import.meta.env.VITE_APP_SUBMIT_BET,
  UPCOMING_ROUNDS: import.meta.env.VITE_APP_UPCOMING_ROUNDS,
  TOP_BETS: import.meta.env.VITE_APP_TOP_BETS,
  HOT_COLD: import.meta.env.VITE_APP_HOT_COLD,
  GET_BET_TYPES: import.meta.env.VITE_APP_GET_BET_TYPES,
  GET_CURRENCY_SYMBOL: import.meta.env.VITE_APP_GET_CURRENCY_SYMBOL,
  GET_PLAYER_HISTORY: import.meta.env.VITE_APP_GET_PLAYER_HISTORY,
  GET_GRAPH_DATA: import.meta.env.VITE_APP_GET_GRAPH_DATA,
  GET_AMOUNT_WON: import.meta.env.VITE_APP_GET_AMOUNT_WON,
  GET_PLAYER_HISTORY_NAV: import.meta.env.VITE_APP_GET_PLAYER_HISTORY_NAV,
}

export const fetchBalance = async (
  token: string,
  setBalance: (balance: number) => void,
  setPlacedChips: (placedChips: SelectableCell[]) => void
): Promise<number | null | undefined> => {
  // Use authMiddleware to get headers
  const headers = authMiddleware.getAuthHeader(token)

  // Use apiClient directly instead of tryCatch + get
  const [data, error] = await apiClient.get<number>(
    ENDPOINTS.BALANCE,
    null,
    headers
  )

  if (error) {
    logger.error("Error fetching balance", error, { context: "API" })

    toastService.error(
      "Balance Update Failed",
      "Unable to fetch your current balance. Please refresh."
    )
    return null
  }

  if (data !== null) {
    setBalance(data ?? 0)
    setPlacedChips([])
    return data
  }
}

const validateCurrencyRequest = (token: string): boolean => {
  if (!token || token.trim() === "") {
    logger.warn("Missing authentication token for currency request", {
      context: "API",
    })
    return false
  }
  return true
}

const handleCurrencyApiError = (error: any): string | null => {
  if (error?.status === 400) {
    logger.error(
      "Bad request for currency symbol - invalid parameters",
      error,
      {
        context: "API",
        data: {
          status: error.status,
          message: error.message,
          details: error.details,
        },
      }
    )

    // Provide user-friendly feedback for 400 errors
    if (error.details?.message) {
      logger.info(`Currency API error details: ${error.details.message}`, {
        context: "API",
      })
    }
  } else {
    logger.error("Error fetching currency symbol", error, {
      context: "API",
      data: { status: error?.status },
    })
  }
  return null
}

export const fetchCurrencyCode = async (
  token: string
): Promise<string | null | undefined> => {
  // Validate request before sending
  if (!validateCurrencyRequest(token)) {
    return null
  }

  // Use authMiddleware to get headers
  const headers = authMiddleware.getAuthHeader(token)

  // Add additional headers that might be required
  const requestHeaders = {
    ...headers,
    Accept: "application/json",
    "Content-Type": "application/json",
  }

  logger.debug("Fetching currency symbol", {
    context: "API",
    data: { endpoint: ENDPOINTS.GET_CURRENCY_SYMBOL },
  })

  // Use apiClient with proper error handling
  const [data, error] = await apiClient.get<CurrencyResponse>(
    ENDPOINTS.GET_CURRENCY_SYMBOL,
    null,
    requestHeaders
  )

  if (error) {
    return handleCurrencyApiError(error)
  }

  // Validate response structure
  if (!data) {
    logger.error("No data received from currency API", null, {
      context: "API",
      data: { endpoint: ENDPOINTS.GET_CURRENCY_SYMBOL },
    })
    return null
  }

  if (data?.response?.currencySymbol) {
    logger.debug("Successfully fetched currency symbol", {
      context: "API",
      data: { symbol: data.response.currencySymbol },
    })
    return data.response.currencySymbol
  }

  logger.error("Invalid currency response structure", null, {
    context: "API",
    data: {
      responseKeys: data?.response ? Object.keys(data.response) : [],
      hasResponse: !!data?.response,
    },
  })
  return null
}

export const getWinnings = async (betSlipId: number): Promise<number> => {
  // Use API client
  const [data, error] = await apiClient.get<number>(
    `${ENDPOINTS.GET_AMOUNT_WON}?betSlipId=${betSlipId}`
  )

  if (error) {
    // Use logger
    logger.error("Error fetching winnings", error, { context: "API" })
    return 0
  }

  return data ?? 0
}

interface BetSubmissionResponse {
  code: number
  message?: string
  betSlipId?: number
}

export const submitBet = async (
  betSubmission: RouletteBetRequest,
  token: string,
  setPlacedChips: (placedChips: SelectableCell[]) => void,
  setBetslipId: (betslipId: number) => void,
  setBalance: (balance: number) => void
): Promise<boolean | undefined> => {
  // Use performance monitoring
  // Use auth middleware
  const headers = authMiddleware.getAuthHeader(token)

  // Use API client
  const [data, error] = await apiClient.post<BetSubmissionResponse>(
    ENDPOINTS.SUBMIT_BET,
    betSubmission,
    headers
  )

  if (error) {
    // Use logger
    logger.error("Error submitting bet", error, {
      context: "Betting",
      data: { betSubmission },
    })

    // Use toast service
    toastService.error("Error submitting bet", "Bet has been cancelled")

    setBetslipId(0) // Reset betslipId on error
    return false
  }

  if (!data) {
    logger.error("No response received from bet submission", null, {
      context: "Betting",
      data: { betSubmission },
    })
    setBetslipId(0) // Reset betslipId on failed submission
    return false
  }

  if (data.code !== 0) {
    // Use toast service
    toastService.error(
      "Error submitting bet",
      data.message || "Unknown error occurred"
    )
    await fetchBalance(token, setBalance, setPlacedChips)
    setPlacedChips([])
    setBetslipId(0) // Reset betslipId on invalid submission
    return false
  }

  if (data.betSlipId) {
    setBetslipId(data.betSlipId)
  }

  return true
}

interface HistoryRound {
  rouletteNumber: number
  bonusNumber: number
}

export const getHistory = async (lookBehind: number): Promise<History[]> => {
  // Use API client
  const [data, error] = await apiClient.get<HistoryRound[]>(
    `${ENDPOINTS.UPCOMING_ROUNDS}?lookAheadMinutes=-${lookBehind}`
  )

  if (error) {
    // Use logger
    logger.error("Error fetching history", error, { context: "API" })
    return []
  }

  return (
    data?.map((round) => ({
      rouletteNumber: round.rouletteNumber,
      bonusNumber: round.bonusNumber,
    })) ?? []
  )
}

export const getBetTypes = async (): Promise<BetTypes[]> => {
  // Use API client
  const [data, error] = await apiClient.get<BetTypes[]>(ENDPOINTS.GET_BET_TYPES)

  if (error) {
    // Use logger
    logger.error("Error fetching bet types", error, { context: "API" })
    return []
  }

  return data ?? []
}

type Bet = {
  betNumber: number
  timesSelected: number
  totalStake: number
}

export interface TopBetsAndBonuses {
  betNumbers: Bet[]
  betBonuses: Bet[]
}

export const getPlayerBetHistory = async (
  playerGuid: string,
  token: string
): Promise<PlayerBetHistory[]> => {
  // Use auth middleware
  const headers = authMiddleware.getAuthHeader(token)

  // Use API client
  const [data, error] = await apiClient.get<PlayerBetHistory[]>(
    `${ENDPOINTS.GET_PLAYER_HISTORY}?playerGuid=${playerGuid}`,
    null,
    headers
  )

  if (error) {
    // Use logger
    logger.error("Error fetching player bet history", error, { context: "API" })
    return []
  }

  return data ?? []
}

export const getPlayerBetHistoryNav = async (
  playerGuid: string,
  token: string
): Promise<BetHistoryNav[]> => {
  // Use auth middleware
  const headers = authMiddleware.getAuthHeader(token)

  // Use API client - use any type to handle potential string response
  const [data, error] = await apiClient.get<BetHistoryNav[] | string>(
    `${ENDPOINTS.GET_PLAYER_HISTORY_NAV}?playerGuid=${playerGuid}`,
    null,
    headers
  )

  if (error) {
    // Use logger
    logger.error("Error fetching player bet history navigation", error, {
      context: "API",
    })
    return []
  }

  // Handle string response (error message)
  if (data && typeof data === "string") {
    if (data.includes("No history")) {
      return []
    }
  }

  if (!data || !Array.isArray(data)) {
    // Use logger
    logger.error(
      "Unexpected response format for player bet history navigation",
      null,
      {
        context: "API",
        data,
      }
    )
    return []
  }

  return data
}
