import type React from "react"
import Divider from "@/components/ui/Divider"
import { cn } from "@/lib/utils"
import { BONUS_PANEL_STYLES } from "../constants/bonus-panel-styles"

interface DragonLayoutProps {
  isMobile: boolean
  spritesheetRef: React.RefObject<HTMLImageElement>
}

/**
 * Component for displaying the dragon animation layout
 */
export const DragonLayout: React.FC<DragonLayoutProps> = ({ 
  isMobile, 
  spritesheetRef 
}) => (
  <section className='grid grid-cols-1 lg:grid-cols-[0.5fr_1fr_0.5fr] grid-rows-[auto_auto_auto] h-full pt-1 pb-3'>
    {isMobile && (
      <div className='w-full'>
        <p className='text-2xl px-12 text-center uppercase text-[#bf9a5d] font-bold '>
          bonus bet
        </p>
        <Divider className='w-full' />
      </div>
    )}
    {!isMobile && (
      <>
        <div className={cn(BONUS_PANEL_STYLES.verticalText, "row-start-1 col-start-1")}>
          {"BONUS".split("").map((char, index) => (
            <span
              key={`bonus-${index}`}
              className='text-3xl leading-none text-stroke text-stroke-2 text-stroke-amber-500'
            >
              {char}
            </span>
          ))}
        </div>
        <Divider className='w-full row-start-2 col-start-1 my-2' />
      </>
    )}
    <div className='lg:row-span-3 row-span-12 relative w-full h-full self-center overflow-hidden'>
      {/* Dragon container with responsive scaling */}
      <div className='relative w-full h-full scale-[0.9] transform-gpu'>
        <img
          className='absolute -top-6 left-[40%] z-[4]  w-[80%]  max-w-full max-h-full object-contain'
          src='/assets/images/dragon/head.png'
          alt='head'
        />
        <img
          className='absolute -top-6 left-0 z-[2] w-[80%] max-w-full object-contain'
          src='/assets/images/dragon/neck.png'
          alt='tail'
        />
        <span className='absolute lg:left-10 left-7 -bottom-14 w-3/4 h-full z-[3]'>
          <img src='/assets/images/dragon/gold-border.webp' alt='border' />
        </span>
        <span className='absolute lg:left-14 left-7 top-12 w-3/4 h-[75%] overflow-hidden z-[3]'>
          <img
            ref={spritesheetRef}
            src='/assets/images/dragon/bonus-sprite-sheet.webp'
            alt='bonus sprite sheet'
          />
        </span>
        <img
          className='absolute top-1/2 left-5 z-[3] w-[24%]  max-w-full max-h-full object-contain'
          src='/assets/images/dragon/hand-right.png'
          alt='right hand'
        />
        <img
          className='absolute top-1/2 right-0 z-[3] w-[20%] max-w-full max-h-full object-contain'
          src='/assets/images/dragon/hand-left.png'
          alt='left hand'
        />
        <img
          className='absolute -bottom-5 left-[0%] z-[4] w-full max-w-full max-h-full object-contain'
          src='/assets/images/dragon/body-front.png'
          alt='body front'
        />
        <img
          className='absolute -bottom-5 left-[1%] z-[1] w-full max-w-full max-h-full object-contain'
          src='/assets/images/dragon/body-back.png'
          alt='body'
        />
      </div>
    </div>
    {!isMobile && (
      <>
        <Divider className='w-full row-start-2 col-start-3' />
        <div
          className={cn(BONUS_PANEL_STYLES.verticalText, "row-start-3 col-start-3 pb-5")}
        >
          {"BET".split("").map((char, index) => (
            <span
              key={`bets-${index}`}
              className='text-3xl leading-none text-stroke text-stroke-2 text-stroke-amber-500'
            >
              {char}
            </span>
          ))}
        </div>
      </>
    )}
  </section>
)
