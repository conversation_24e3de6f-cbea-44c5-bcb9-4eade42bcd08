<?xml version="1.0" encoding="UTF-8"?>
<svg id="dragon" xmlns="http://www.w3.org/2000/svg" width="940.6637" height="3934.2519" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 940.6637 3934.2519">
  <defs>
    <style>
      .cls-1 {
        clip-path: url(#clippath);
      }

      .cls-2 {
        opacity: .5;
      }
    </style>
    <clipPath id="clippath">
      <rect id="scroll-area-2" data-name="scroll-area" class="cls-2" x="204.8153" y="1731.7912" width="522.8639" height="925.4295"/>
    </clipPath>
  </defs>
  <image id="neck" width="1348" height="2056" transform="translate(29.8246 1419.1847) scale(.4989)" xlink:href="neck.png"/>
  <image id="body-back" width="1961" height="1039" transform="translate(0 2428.1524) scale(.4485)" xlink:href="body-back.png"/>
  <g id="scroll-wheel">
    <rect id="scroll-area" class="cls-2" x="204.8153" y="1731.7912" width="522.8639" height="925.4295"/>
    <g class="cls-1">
      <image id="sprite-sheet" width="130" height="989" transform="translate(207.4131) scale(3.978)" xlink:href="bonus-sprite-sheet.webp"/>
    </g>
  </g>
  <image id="frame-bottom" width="148" height="199" transform="translate(196.382 1717.3294) scale(3.6433)" xlink:href="frame-top.png"/>
  <image id="frame-top" width="148" height="153" transform="translate(196.382 2117.44) scale(3.6433 3.577)" xlink:href="frame-bottom.png"/>
  <image id="body-front" width="1915" height="814" transform="translate(0 2527.6828) scale(.4499)" xlink:href="body-front.png"/>
  <image id="head" width="1116" height="1262" transform="translate(369.208 1320.9099) scale(.5121)" xlink:href="head.png"/>
  <image id="right-hand" width="292" height="443" transform="translate(647.0501 2034.3354) scale(.5523 .5402)" xlink:href="hand-left.png"/>
  <image id="left-hand" width="376" height="423" transform="translate(92.0201 2043.142) scale(.5383)" xlink:href="hand-right.png"/>
</svg>