import { useMemo } from "react"
import { formatting } from "@/lib/formatting"
import { logger } from "@/lib/logger"
import { rouletteChips, type Chip } from "@config/chip-config"

// Re-export the Chip type for convenience
export type { Chip }

// Note: formatChipValue has been moved to src/lib/formatting.ts
// Import it from there instead: import { formatChipValue } from '@/lib/formatting'

/**
 * Find the appropriate chip representation for a given value
 *
 * @param value - Value to find chip for
 * @returns Chip object with the appropriate representation
 */
export const findLiteralChip = (value: number): Chip | null => {
  try {
    const sanitizedValue = Math.max(0, Math.round(value))
    const sortedChips = [...rouletteChips]
      .filter((chip) => chip.value > 0)
      .sort((a, b) => b.value - a.value)

    const chipRepresentation =
      sortedChips.find((chip) => chip.value <= sanitizedValue) || null

    return {
      ...chipRepresentation,
      value: value,
    } as Chip
  } catch (error) {
    logger.error("Error finding literal chip", error, {
      context: "ChipMiddleware",
      data: { value },
    })
    return null
  }
}

/**
 * Hook to access chip configuration
 *
 * @returns Object with chip utilities and configuration
 */
export const useChips = () => {
  // Memoize the sorted chips to prevent unnecessary re-sorting
  const sortedChips = useMemo(() => {
    return [...rouletteChips].sort((a, b) => a.value - b.value)
  }, [])

  return {
    chips: sortedChips,
    formatChipValue: formatting.chipValue, // Use the consolidated formatting utility
    findLiteralChip,
    getChipByValue: (value: number) =>
      sortedChips.find((chip) => chip.value === value) || null,
  }
}
