const baseUrl = import.meta.env.VITE_APP_SIDE_BASE_URL

export const soundMap = {
  "bg-music": { type: "music", src: `${baseUrl}audio/bg-music.mp3` },
  ambience: { type: "music", src: `${baseUrl}audio/ambience.mp3` },
  "chip-1": { type: "sfx", src: `${baseUrl}audio/chip-1.mp3` },
  "chip-2": { type: "sfx", src: `${baseUrl}audio/chip-2.mp3` },

  "rsa-greeting": {
    type: "sfx",
    src: `${baseUrl}audio/RSA/greetings/welcome.wav`,
  },
  "rsa-closed": {
    type: "sfx",
    src: `${baseUrl}audio/RSA/call-outs/bets-closed.wav`,
  },
  "rsa-open": {
    type: "sfx",
    src: `${baseUrl}audio/RSA/call-outs/bets-open.wav`,
  },
  "rsa-five-sec": {
    type: "sfx",
    src: `${baseUrl}audio/RSA/call-outs/five-sec.wav`,
  },
  "rsa-ten-sec": {
    type: "sfx",
    src: `${baseUrl}audio/RSA/call-outs/ten-sec.wav`,
  },

  "rsa-bonus-1": { type: "sfx", src: `${baseUrl}audio/RSA/bonuses/1.wav` },
  "rsa-bonus-2": { type: "sfx", src: `${baseUrl}audio/RSA/bonuses/2.wav` },
  "rsa-bonus-3": { type: "sfx", src: `${baseUrl}audio/RSA/bonuses/3.wav` },
  "rsa-bonus-4": { type: "sfx", src: `${baseUrl}audio/RSA/bonuses/4.wav` },
  "rsa-bonus-5": { type: "sfx", src: `${baseUrl}audio/RSA/bonuses/5.wav` },
  "rsa-bonus-6": { type: "sfx", src: `${baseUrl}audio/RSA/bonuses/6.wav` },
  "rsa-bonus-7": { type: "sfx", src: `${baseUrl}audio/RSA/bonuses/7.wav` },

  "rsa-number-0": {
    type: "sfx",
    src: `${baseUrl}audio/RSA/numbers-colours/0.wav`,
  },
  "rsa-number-1": {
    type: "sfx",
    src: `${baseUrl}audio/RSA/numbers-colours/1.wav`,
  },
  "rsa-number-2": {
    type: "sfx",
    src: `${baseUrl}audio/RSA/numbers-colours/2.wav`,
  },
  "rsa-number-3": {
    type: "sfx",
    src: `${baseUrl}audio/RSA/numbers-colours/3.wav`,
  },
  "rsa-number-4": {
    type: "sfx",
    src: `${baseUrl}audio/RSA/numbers-colours/4.wav`,
  },
  "rsa-number-5": {
    type: "sfx",
    src: `${baseUrl}audio/RSA/numbers-colours/5.wav`,
  },
  "rsa-number-6": {
    type: "sfx",
    src: `${baseUrl}audio/RSA/numbers-colours/6.wav`,
  },
  "rsa-number-7": {
    type: "sfx",
    src: `${baseUrl}audio/RSA/numbers-colours/7.wav`,
  },
  "rsa-number-8": {
    type: "sfx",
    src: `${baseUrl}audio/RSA/numbers-colours/8.wav`,
  },
  "rsa-number-9": {
    type: "sfx",
    src: `${baseUrl}audio/RSA/numbers-colours/9.wav`,
  },
  "rsa-number-10": {
    type: "sfx",
    src: `${baseUrl}audio/RSA/numbers-colours/10.wav`,
  },
  "rsa-number-11": {
    type: "sfx",
    src: `${baseUrl}audio/RSA/numbers-colours/11.wav`,
  },
  "rsa-number-12": {
    type: "sfx",
    src: `${baseUrl}audio/RSA/numbers-colours/12.wav`,
  },
  "rsa-number-13": {
    type: "sfx",
    src: `${baseUrl}audio/RSA/numbers-colours/13.wav`,
  },
  "rsa-number-14": {
    type: "sfx",
    src: `${baseUrl}audio/RSA/numbers-colours/14.wav`,
  },
  "rsa-number-15": {
    type: "sfx",
    src: `${baseUrl}audio/RSA/numbers-colours/15.wav`,
  },
  "rsa-number-16": {
    type: "sfx",
    src: `${baseUrl}audio/RSA/numbers-colours/16.wav`,
  },
  "rsa-number-17": {
    type: "sfx",
    src: `${baseUrl}audio/RSA/numbers-colours/17.wav`,
  },
  "rsa-number-18": {
    type: "sfx",
    src: `${baseUrl}audio/RSA/numbers-colours/18.wav`,
  },
  "rsa-number-19": {
    type: "sfx",
    src: `${baseUrl}audio/RSA/numbers-colours/19.wav`,
  },
  "rsa-number-20": {
    type: "sfx",
    src: `${baseUrl}audio/RSA/numbers-colours/20.wav`,
  },
  "rsa-number-21": {
    type: "sfx",
    src: `${baseUrl}audio/RSA/numbers-colours/21.wav`,
  },
  "rsa-number-22": {
    type: "sfx",
    src: `${baseUrl}audio/RSA/numbers-colours/22.wav`,
  },
  "rsa-number-23": {
    type: "sfx",
    src: `${baseUrl}audio/RSA/numbers-colours/23.wav`,
  },
  "rsa-number-24": {
    type: "sfx",
    src: `${baseUrl}audio/RSA/numbers-colours/24.wav`,
  },
  "rsa-number-25": {
    type: "sfx",
    src: `${baseUrl}audio/RSA/numbers-colours/25.wav`,
  },
  "rsa-number-26": {
    type: "sfx",
    src: `${baseUrl}audio/RSA/numbers-colours/26.wav`,
  },
  "rsa-number-27": {
    type: "sfx",
    src: `${baseUrl}audio/RSA/numbers-colours/27.wav`,
  },
  "rsa-number-28": {
    type: "sfx",
    src: `${baseUrl}audio/RSA/numbers-colours/28.wav`,
  },
  "rsa-number-29": {
    type: "sfx",
    src: `${baseUrl}audio/RSA/numbers-colours/29.wav`,
  },
  "rsa-number-30": {
    type: "sfx",
    src: `${baseUrl}audio/RSA/numbers-colours/30.wav`,
  },
  "rsa-number-31": {
    type: "sfx",
    src: `${baseUrl}audio/RSA/numbers-colours/31.wav`,
  },
  "rsa-number-32": {
    type: "sfx",
    src: `${baseUrl}audio/RSA/numbers-colours/32.wav`,
  },
  "rsa-number-33": {
    type: "sfx",
    src: `${baseUrl}audio/RSA/numbers-colours/33.wav`,
  },
  "rsa-number-34": {
    type: "sfx",
    src: `${baseUrl}audio/RSA/numbers-colours/34.wav`,
  },
  "rsa-number-35": {
    type: "sfx",
    src: `${baseUrl}audio/RSA/numbers-colours/35.wav`,
  },
  "rsa-number-36": {
    type: "sfx",
    src: `${baseUrl}audio/RSA/numbers-colours/36.wav`,
  },

  "eu-greeting": {
    type: "sfx",
    src: `${baseUrl}audio/EU/greetings/welcome.wav`,
  },
  "eu-closed": {
    type: "sfx",
    src: `${baseUrl}audio/EU/call-outs/bets-closed.wav`,
  },
  "eu-open": {
    type: "sfx",
    src: `${baseUrl}audio/EU/call-outs/bets-open.wav`,
  },
  "eu-five-sec": {
    type: "sfx",
    src: `${baseUrl}audio/EU/call-outs/five-sec.wav`,
  },
  "eu-ten-sec": {
    type: "sfx",
    src: `${baseUrl}audio/EU/call-outs/ten-sec.wav`,
  },

  "eu-bonus-1": { type: "sfx", src: `${baseUrl}audio/EU/bonuses/1.wav` },
  "eu-bonus-2": { type: "sfx", src: `${baseUrl}audio/EU/bonuses/2.wav` },
  "eu-bonus-3": { type: "sfx", src: `${baseUrl}audio/EU/bonuses/3.wav` },
  "eu-bonus-4": { type: "sfx", src: `${baseUrl}audio/EU/bonuses/4.wav` },
  "eu-bonus-5": { type: "sfx", src: `${baseUrl}audio/EU/bonuses/5.wav` },
  "eu-bonus-6": { type: "sfx", src: `${baseUrl}audio/EU/bonuses/6.wav` },
  "eu-bonus-7": { type: "sfx", src: `${baseUrl}audio/EU/bonuses/7.wav` },

  "eu-number-0": {
    type: "sfx",
    src: `${baseUrl}audio/EU/numbers-colours/0.wav`,
  },
  "eu-number-1": {
    type: "sfx",
    src: `${baseUrl}audio/EU/numbers-colours/1.wav`,
  },
  "eu-number-2": {
    type: "sfx",
    src: `${baseUrl}audio/EU/numbers-colours/2.wav`,
  },
  "eu-number-3": {
    type: "sfx",
    src: `${baseUrl}audio/EU/numbers-colours/3.wav`,
  },
  "eu-number-4": {
    type: "sfx",
    src: `${baseUrl}audio/EU/numbers-colours/4.wav`,
  },
  "eu-number-5": {
    type: "sfx",
    src: `${baseUrl}audio/EU/numbers-colours/5.wav`,
  },
  "eu-number-6": {
    type: "sfx",
    src: `${baseUrl}audio/EU/numbers-colours/6.wav`,
  },
  "eu-number-7": {
    type: "sfx",
    src: `${baseUrl}audio/EU/numbers-colours/7.wav`,
  },
  "eu-number-8": {
    type: "sfx",
    src: `${baseUrl}audio/EU/numbers-colours/8.wav`,
  },
  "eu-number-9": {
    type: "sfx",
    src: `${baseUrl}audio/EU/numbers-colours/9.wav`,
  },
  "eu-number-10": {
    type: "sfx",
    src: `${baseUrl}audio/EU/numbers-colours/10.wav`,
  },
  "eu-number-11": {
    type: "sfx",
    src: `${baseUrl}audio/EU/numbers-colours/11.wav`,
  },
  "eu-number-12": {
    type: "sfx",
    src: `${baseUrl}audio/EU/numbers-colours/12.wav`,
  },
  "eu-number-13": {
    type: "sfx",
    src: `${baseUrl}audio/EU/numbers-colours/13.wav`,
  },
  "eu-number-14": {
    type: "sfx",
    src: `${baseUrl}audio/EU/numbers-colours/14.wav`,
  },
  "eu-number-15": {
    type: "sfx",
    src: `${baseUrl}audio/EU/numbers-colours/15.wav`,
  },
  "eu-number-16": {
    type: "sfx",
    src: `${baseUrl}audio/EU/numbers-colours/16.wav`,
  },
  "eu-number-17": {
    type: "sfx",
    src: `${baseUrl}audio/EU/numbers-colours/17.wav`,
  },
  "eu-number-18": {
    type: "sfx",
    src: `${baseUrl}audio/EU/numbers-colours/18.wav`,
  },
  "eu-number-19": {
    type: "sfx",
    src: `${baseUrl}audio/EU/numbers-colours/19.wav`,
  },
  "eu-number-20": {
    type: "sfx",
    src: `${baseUrl}audio/EU/numbers-colours/20.wav`,
  },
  "eu-number-21": {
    type: "sfx",
    src: `${baseUrl}audio/EU/numbers-colours/21.wav`,
  },
  "eu-number-22": {
    type: "sfx",
    src: `${baseUrl}audio/EU/numbers-colours/22.wav`,
  },
  "eu-number-23": {
    type: "sfx",
    src: `${baseUrl}audio/EU/numbers-colours/23.wav`,
  },
  "eu-number-24": {
    type: "sfx",
    src: `${baseUrl}audio/EU/numbers-colours/24.wav`,
  },
  "eu-number-25": {
    type: "sfx",
    src: `${baseUrl}audio/EU/numbers-colours/25.wav`,
  },
  "eu-number-26": {
    type: "sfx",
    src: `${baseUrl}audio/EU/numbers-colours/26.wav`,
  },
  "eu-number-27": {
    type: "sfx",
    src: `${baseUrl}audio/EU/numbers-colours/27.wav`,
  },
  "eu-number-28": {
    type: "sfx",
    src: `${baseUrl}audio/EU/numbers-colours/28.wav`,
  },
  "eu-number-29": {
    type: "sfx",
    src: `${baseUrl}audio/EU/numbers-colours/29.wav`,
  },
  "eu-number-30": {
    type: "sfx",
    src: `${baseUrl}audio/EU/numbers-colours/30.wav`,
  },
  "eu-number-31": {
    type: "sfx",
    src: `${baseUrl}audio/EU/numbers-colours/31.wav`,
  },
  "eu-number-32": {
    type: "sfx",
    src: `${baseUrl}audio/EU/numbers-colours/32.wav`,
  },
  "eu-number-33": {
    type: "sfx",
    src: `${baseUrl}audio/EU/numbers-colours/33.wav`,
  },
  "eu-number-34": {
    type: "sfx",
    src: `${baseUrl}audio/EU/numbers-colours/34.wav`,
  },
  "eu-number-35": {
    type: "sfx",
    src: `${baseUrl}audio/EU/numbers-colours/35.wav`,
  },
  "eu-number-36": {
    type: "sfx",
    src: `${baseUrl}audio/EU/numbers-colours/36.wav`,
  },
  ping: {
    type: "sfx",
    src: `/ping.wav`,
  },

  // isiZulu voice files
  "zulu-greeting": {
    type: "sfx",
    src: `${baseUrl}audio/ZULU/greetings/welcome.wav`,
  },
  "zulu-closed": {
    type: "sfx",
    src: `${baseUrl}audio/ZULU/call-outs/bets-closed.wav`,
  },
  "zulu-open": {
    type: "sfx",
    src: `${baseUrl}audio/ZULU/call-outs/bets-open.wav`,
  },
  "zulu-five-sec": {
    type: "sfx",
    src: `${baseUrl}audio/ZULU/call-outs/five-sec.wav`,
  },
  "zulu-ten-sec": {
    type: "sfx",
    src: `${baseUrl}audio/ZULU/call-outs/ten-sec.wav`,
  },

  "zulu-bonus-1": { type: "sfx", src: `${baseUrl}audio/ZULU/bonuses/1.wav` },
  "zulu-bonus-2": { type: "sfx", src: `${baseUrl}audio/ZULU/bonuses/2.wav` },
  "zulu-bonus-3": { type: "sfx", src: `${baseUrl}audio/ZULU/bonuses/3.wav` },
  "zulu-bonus-4": { type: "sfx", src: `${baseUrl}audio/ZULU/bonuses/4.wav` },
  "zulu-bonus-5": { type: "sfx", src: `${baseUrl}audio/ZULU/bonuses/5.wav` },
  "zulu-bonus-6": { type: "sfx", src: `${baseUrl}audio/ZULU/bonuses/6.wav` },
  "zulu-bonus-7": { type: "sfx", src: `${baseUrl}audio/ZULU/bonuses/7.wav` },

  "zulu-number-0": {
    type: "sfx",
    src: `${baseUrl}audio/ZULU/numbers-colours/0.wav`,
  },
  "zulu-number-1": {
    type: "sfx",
    src: `${baseUrl}audio/ZULU/numbers-colours/1.wav`,
  },
  "zulu-number-2": {
    type: "sfx",
    src: `${baseUrl}audio/ZULU/numbers-colours/2.wav`,
  },
  "zulu-number-3": {
    type: "sfx",
    src: `${baseUrl}audio/ZULU/numbers-colours/3.wav`,
  },
  "zulu-number-4": {
    type: "sfx",
    src: `${baseUrl}audio/ZULU/numbers-colours/4.wav`,
  },
  "zulu-number-5": {
    type: "sfx",
    src: `${baseUrl}audio/ZULU/numbers-colours/5.wav`,
  },
  "zulu-number-6": {
    type: "sfx",
    src: `${baseUrl}audio/ZULU/numbers-colours/6.wav`,
  },
  "zulu-number-7": {
    type: "sfx",
    src: `${baseUrl}audio/ZULU/numbers-colours/7.wav`,
  },
  "zulu-number-8": {
    type: "sfx",
    src: `${baseUrl}audio/ZULU/numbers-colours/8.wav`,
  },
  "zulu-number-9": {
    type: "sfx",
    src: `${baseUrl}audio/ZULU/numbers-colours/9.wav`,
  },
  "zulu-number-10": {
    type: "sfx",
    src: `${baseUrl}audio/ZULU/numbers-colours/10.wav`,
  },
  "zulu-number-11": {
    type: "sfx",
    src: `${baseUrl}audio/ZULU/numbers-colours/11.wav`,
  },
  "zulu-number-12": {
    type: "sfx",
    src: `${baseUrl}audio/ZULU/numbers-colours/12.wav`,
  },
  "zulu-number-13": {
    type: "sfx",
    src: `${baseUrl}audio/ZULU/numbers-colours/13.wav`,
  },
  "zulu-number-14": {
    type: "sfx",
    src: `${baseUrl}audio/ZULU/numbers-colours/14.wav`,
  },
  "zulu-number-15": {
    type: "sfx",
    src: `${baseUrl}audio/ZULU/numbers-colours/15.wav`,
  },
  "zulu-number-16": {
    type: "sfx",
    src: `${baseUrl}audio/ZULU/numbers-colours/16.wav`,
  },
  "zulu-number-17": {
    type: "sfx",
    src: `${baseUrl}audio/ZULU/numbers-colours/17.wav`,
  },
  "zulu-number-18": {
    type: "sfx",
    src: `${baseUrl}audio/ZULU/numbers-colours/18.wav`,
  },
  "zulu-number-19": {
    type: "sfx",
    src: `${baseUrl}audio/ZULU/numbers-colours/19.wav`,
  },
  "zulu-number-20": {
    type: "sfx",
    src: `${baseUrl}audio/ZULU/numbers-colours/20.wav`,
  },
  "zulu-number-21": {
    type: "sfx",
    src: `${baseUrl}audio/ZULU/numbers-colours/21.wav`,
  },
  "zulu-number-22": {
    type: "sfx",
    src: `${baseUrl}audio/ZULU/numbers-colours/22.wav`,
  },
  "zulu-number-23": {
    type: "sfx",
    src: `${baseUrl}audio/ZULU/numbers-colours/23.wav`,
  },
  "zulu-number-24": {
    type: "sfx",
    src: `${baseUrl}audio/ZULU/numbers-colours/24.wav`,
  },
  "zulu-number-25": {
    type: "sfx",
    src: `${baseUrl}audio/ZULU/numbers-colours/25.wav`,
  },
  "zulu-number-26": {
    type: "sfx",
    src: `${baseUrl}audio/ZULU/numbers-colours/26.wav`,
  },
  "zulu-number-27": {
    type: "sfx",
    src: `${baseUrl}audio/ZULU/numbers-colours/27.wav`,
  },
  "zulu-number-28": {
    type: "sfx",
    src: `${baseUrl}audio/ZULU/numbers-colours/28.wav`,
  },
  "zulu-number-29": {
    type: "sfx",
    src: `${baseUrl}audio/ZULU/numbers-colours/29.wav`,
  },
  "zulu-number-30": {
    type: "sfx",
    src: `${baseUrl}audio/ZULU/numbers-colours/30.wav`,
  },
  "zulu-number-31": {
    type: "sfx",
    src: `${baseUrl}audio/ZULU/numbers-colours/31.wav`,
  },
  "zulu-number-32": {
    type: "sfx",
    src: `${baseUrl}audio/ZULU/numbers-colours/32.wav`,
  },
  "zulu-number-33": {
    type: "sfx",
    src: `${baseUrl}audio/ZULU/numbers-colours/33.wav`,
  },
  "zulu-number-34": {
    type: "sfx",
    src: `${baseUrl}audio/ZULU/numbers-colours/34.wav`,
  },
  "zulu-number-35": {
    type: "sfx",
    src: `${baseUrl}audio/ZULU/numbers-colours/35.wav`,
  },
  "zulu-number-36": {
    type: "sfx",
    src: `${baseUrl}audio/ZULU/numbers-colours/36.wav`,
  },

  // isiXhosa voice files
  "xhosa-greeting": {
    type: "sfx",
    src: `${baseUrl}audio/XHOSA/greetings/welcome.wav`,
  },
  "xhosa-closed": {
    type: "sfx",
    src: `${baseUrl}audio/XHOSA/call-outs/bets-closed.wav`,
  },
  "xhosa-open": {
    type: "sfx",
    src: `${baseUrl}audio/XHOSA/call-outs/bets-open.wav`,
  },
  "xhosa-five-sec": {
    type: "sfx",
    src: `${baseUrl}audio/XHOSA/call-outs/five-sec.wav`,
  },
  "xhosa-ten-sec": {
    type: "sfx",
    src: `${baseUrl}audio/XHOSA/call-outs/ten-sec.wav`,
  },

  "xhosa-bonus-1": { type: "sfx", src: `${baseUrl}audio/XHOSA/bonuses/1.wav` },
  "xhosa-bonus-2": { type: "sfx", src: `${baseUrl}audio/XHOSA/bonuses/2.wav` },
  "xhosa-bonus-3": { type: "sfx", src: `${baseUrl}audio/XHOSA/bonuses/3.wav` },
  "xhosa-bonus-4": { type: "sfx", src: `${baseUrl}audio/XHOSA/bonuses/4.wav` },
  "xhosa-bonus-5": { type: "sfx", src: `${baseUrl}audio/XHOSA/bonuses/5.wav` },
  "xhosa-bonus-6": { type: "sfx", src: `${baseUrl}audio/XHOSA/bonuses/6.wav` },
  "xhosa-bonus-7": { type: "sfx", src: `${baseUrl}audio/XHOSA/bonuses/7.wav` },

  "xhosa-number-0": {
    type: "sfx",
    src: `${baseUrl}audio/XHOSA/numbers-colours/0.wav`,
  },
  "xhosa-number-1": {
    type: "sfx",
    src: `${baseUrl}audio/XHOSA/numbers-colours/1.wav`,
  },
  "xhosa-number-2": {
    type: "sfx",
    src: `${baseUrl}audio/XHOSA/numbers-colours/2.wav`,
  },
  "xhosa-number-3": {
    type: "sfx",
    src: `${baseUrl}audio/XHOSA/numbers-colours/3.wav`,
  },
  "xhosa-number-4": {
    type: "sfx",
    src: `${baseUrl}audio/XHOSA/numbers-colours/4.wav`,
  },
  "xhosa-number-5": {
    type: "sfx",
    src: `${baseUrl}audio/XHOSA/numbers-colours/5.wav`,
  },
  "xhosa-number-6": {
    type: "sfx",
    src: `${baseUrl}audio/XHOSA/numbers-colours/6.wav`,
  },
  "xhosa-number-7": {
    type: "sfx",
    src: `${baseUrl}audio/XHOSA/numbers-colours/7.wav`,
  },
  "xhosa-number-8": {
    type: "sfx",
    src: `${baseUrl}audio/XHOSA/numbers-colours/8.wav`,
  },
  "xhosa-number-9": {
    type: "sfx",
    src: `${baseUrl}audio/XHOSA/numbers-colours/9.wav`,
  },
  "xhosa-number-10": {
    type: "sfx",
    src: `${baseUrl}audio/XHOSA/numbers-colours/10.wav`,
  },
  "xhosa-number-11": {
    type: "sfx",
    src: `${baseUrl}audio/XHOSA/numbers-colours/11.wav`,
  },
  "xhosa-number-12": {
    type: "sfx",
    src: `${baseUrl}audio/XHOSA/numbers-colours/12.wav`,
  },
  "xhosa-number-13": {
    type: "sfx",
    src: `${baseUrl}audio/XHOSA/numbers-colours/13.wav`,
  },
  "xhosa-number-14": {
    type: "sfx",
    src: `${baseUrl}audio/XHOSA/numbers-colours/14.wav`,
  },
  "xhosa-number-15": {
    type: "sfx",
    src: `${baseUrl}audio/XHOSA/numbers-colours/15.wav`,
  },
  "xhosa-number-16": {
    type: "sfx",
    src: `${baseUrl}audio/XHOSA/numbers-colours/16.wav`,
  },
  "xhosa-number-17": {
    type: "sfx",
    src: `${baseUrl}audio/XHOSA/numbers-colours/17.wav`,
  },
  "xhosa-number-18": {
    type: "sfx",
    src: `${baseUrl}audio/XHOSA/numbers-colours/18.wav`,
  },
  "xhosa-number-19": {
    type: "sfx",
    src: `${baseUrl}audio/XHOSA/numbers-colours/19.wav`,
  },
  "xhosa-number-20": {
    type: "sfx",
    src: `${baseUrl}audio/XHOSA/numbers-colours/20.wav`,
  },
  "xhosa-number-21": {
    type: "sfx",
    src: `${baseUrl}audio/XHOSA/numbers-colours/21.wav`,
  },
  "xhosa-number-22": {
    type: "sfx",
    src: `${baseUrl}audio/XHOSA/numbers-colours/22.wav`,
  },
  "xhosa-number-23": {
    type: "sfx",
    src: `${baseUrl}audio/XHOSA/numbers-colours/23.wav`,
  },
  "xhosa-number-24": {
    type: "sfx",
    src: `${baseUrl}audio/XHOSA/numbers-colours/24.wav`,
  },
  "xhosa-number-25": {
    type: "sfx",
    src: `${baseUrl}audio/XHOSA/numbers-colours/25.wav`,
  },
  "xhosa-number-26": {
    type: "sfx",
    src: `${baseUrl}audio/XHOSA/numbers-colours/26.wav`,
  },
  "xhosa-number-27": {
    type: "sfx",
    src: `${baseUrl}audio/XHOSA/numbers-colours/27.wav`,
  },
  "xhosa-number-28": {
    type: "sfx",
    src: `${baseUrl}audio/XHOSA/numbers-colours/28.wav`,
  },
  "xhosa-number-29": {
    type: "sfx",
    src: `${baseUrl}audio/XHOSA/numbers-colours/29.wav`,
  },
  "xhosa-number-30": {
    type: "sfx",
    src: `${baseUrl}audio/XHOSA/numbers-colours/30.wav`,
  },
  "xhosa-number-31": {
    type: "sfx",
    src: `${baseUrl}audio/XHOSA/numbers-colours/31.wav`,
  },
  "xhosa-number-32": {
    type: "sfx",
    src: `${baseUrl}audio/XHOSA/numbers-colours/32.wav`,
  },
  "xhosa-number-33": {
    type: "sfx",
    src: `${baseUrl}audio/XHOSA/numbers-colours/33.wav`,
  },
  "xhosa-number-34": {
    type: "sfx",
    src: `${baseUrl}audio/XHOSA/numbers-colours/34.wav`,
  },
  "xhosa-number-35": {
    type: "sfx",
    src: `${baseUrl}audio/XHOSA/numbers-colours/35.wav`,
  },
  "xhosa-number-36": {
    type: "sfx",
    src: `${baseUrl}audio/XHOSA/numbers-colours/36.wav`,
  },
} as const

export type SoundKeys = keyof typeof soundMap
