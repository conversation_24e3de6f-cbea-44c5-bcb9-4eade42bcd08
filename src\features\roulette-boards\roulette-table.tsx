import React, { useCallback, useEffect, useRef, useState } from "react"
import { useRoundPhase } from "@/hooks"
import { useMobile } from "@/hooks/use-mobile"
import { cn } from "@/lib/utils"
import { useBettingStore } from "@/stores/betting-store"
import { useGameStateStore } from "@/stores/game-state-store"
import { useSettingsStore } from "@/stores/settings-store"
import { SoundKeys } from "@config/audio-config"
import { SelectableCell } from "@config/selector-config"

// Import components
import { Zeros, Orphalins, Tiers, Voisins } from "@config/specials-config"
import { FunctionLayer } from "./function-layer"
import MobileSpecialsTable from "./mobile-specials-table"
import { NeighboursButtons } from "./neighbours"
import { RenderLayer } from "./render-layer"
import SpecialsTable from "./specials-table"
import { STYLES } from "./table-styles"

const RouletteTable = () => {
  const { Betting, Spinning } = useRoundPhase()
  const isMobile = useMobile()
  const gameType = useGameStateStore((state) => state.gameType)
  const addChip = useBettingStore((state) => state.addChip)
  const selectedChip = useBettingStore((state) => state.selectedChip)
  const playSound = useSettingsStore((state) => state.playSound)

  const [hoveredCell, setHoveredCell] = useState<SelectableCell | null>(null)
  const tableRef = useRef<HTMLElement | null>(null)
  const isMountedRef = useRef(true)

  // Handle mouse enter on a cell
  const handleMouseEnter = useCallback(
    (cell: SelectableCell) => {
      if (Betting && isMountedRef.current) {
        setHoveredCell(cell)
      }
    },
    [Betting]
  )

  // Handle cell click
  const handleCellClick = useCallback(
    (cell: SelectableCell) => {
      if (
        Betting &&
        selectedChip &&
        cell.isSelectable &&
        isMountedRef.current
      ) {
        const num = Math.floor(Math.random() * 2) + 1
        playSound(`chip-${num}` as SoundKeys, false)
        addChip(cell, selectedChip)
      }
    },
    [Betting, selectedChip, addChip, playSound]
  )

  // Global mouse event handler
  useEffect(() => {
    const handleMouseLeave = (e: MouseEvent) => {
      if (
        isMountedRef.current &&
        tableRef.current &&
        !tableRef.current.contains(e.target as Node)
      ) {
        setHoveredCell(null)
      }
    }

    document.addEventListener("mousemove", handleMouseLeave)

    return () => {
      document.removeEventListener("mousemove", handleMouseLeave)
    }
  }, [])

  // Clear hovered cell when spinning
  useEffect(() => {
    if (Spinning && isMountedRef.current) {
      setHoveredCell(null)
    }
  }, [Spinning])

  // Cleanup on unmount
  useEffect(() => {
    isMountedRef.current = true

    return () => {
      isMountedRef.current = false
      setHoveredCell(null)
    }
  }, [])

  const handleSpecialButtonClick = (cells: SelectableCell[]) => {
    if (Betting) {
      cells.forEach((cell) => {
        addChip(cell, selectedChip)
      })
    }
  }

  // Special button configurations
  const specialButtonGroups = [
    {
      position: "left-[12%]",
      buttons: [
        {
          cells: Tiers,
          styleClass: STYLES.specialsButtons.smallSeries,
          text: "Small",
          subText: "Series",
        },
        {
          cells: Orphalins,
          styleClass: STYLES.specialsButtons.orphans,
          text: "Orphalins",
          extraTextClass: "py-2",
        },
      ],
    },
    {
      position: "right-[13%]",
      buttons: [
        {
          cells: Zeros,
          styleClass: STYLES.specialsButtons.zeroSpiel,
          text: "Zero",
          subText: "Spiel",
        },
        {
          cells: Voisins,
          styleClass: STYLES.specialsButtons.bigSeries,
          text: "Big",
          subText: "Series",
        },
      ],
    },
  ]

  const renderSpecialButton = (
    button: (typeof specialButtonGroups)[0]["buttons"][0]
  ) => (
    <button
      key={button.text}
      onClick={() => handleSpecialButtonClick(button.cells)}
      className={cn(
        "hover:scale-105 duration-100 ease-in-out cursor-pointer",
        button.styleClass,
        STYLES.specialsButtons.buttonBase
      )}
    >
      <p
        className={cn(
          STYLES.specialsButtons.textContainer,
          button.extraTextClass
        )}
      >
        {button.text}
        {button.subText && (
          <>
            <br />
            {button.subText}
          </>
        )}
      </p>
    </button>
  )

  return (
    <div
      className={cn("relative", STYLES.mainContainer)}
      ref={tableRef as React.RefObject<HTMLDivElement>}
    >
      {gameType === "special" && !isMobile && (
        <>
          {specialButtonGroups.map((group, groupIndex) => (
            <div
              key={groupIndex}
              className={`gap-10 flex flex-col absolute top-1/2 ${group.position} -translate-y-1/2`}
            >
              {group.buttons.map(renderSpecialButton)}
            </div>
          ))}
        </>
      )}
      {gameType === "special" &&
        (isMobile ? <MobileSpecialsTable /> : <SpecialsTable />)}

      <div
        id='normal-table'
        className={cn(
          "relative w-full h-full",
          gameType === "special" &&
            !isMobile &&
            "absolute z-20 scale-60 top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2"
        )}
      >
        <RenderLayer hoveredCell={hoveredCell} />
        <FunctionLayer
          handleCellClick={handleCellClick}
          handleMouseEnter={handleMouseEnter}
        />
      </div>
    </div>
  )
}

export default RouletteTable
