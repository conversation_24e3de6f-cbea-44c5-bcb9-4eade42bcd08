import { useEffect, useRef, useState } from "react"
import Divider from "@/components/ui/Divider"
import GameSection from "@/components/ui/game-section"
import { useRoundPhase, useSignalR } from "@/hooks"
import { useAudioController } from "@/hooks/game/use-audio-controller"
import { useControlPanels } from "@/hooks/game/use-control-panels"
import { useGameNotifications } from "@/hooks/game/use-game-notifications"
import { useInitialization } from "@/lib/initialization"
import { useGameStateStore } from "@/stores/game-state-store"
import { BonusBetsPanel } from "../features/bonus-bets"
import { ResponsiveControlPanel } from "../features/bottom-navigation"
import PastResultsPanel from "../features/past-results/past-results-panel"
import RouletteTable from "../features/roulette-boards/roulette-table"
import SideMenu from "../features/side-menu/side-menu"
import StatisticsPanel from "../features/statistics/statistics-panel"
import VideoPlayer from "../features/streaming/video-player"
import WinnersPanel from "../features/winners/winners-panel"

function Retail() {
  useAudioController()
  useInitialization()
  useGameNotifications()
  const { Resulting } = useRoundPhase()
  const roundData = useGameStateStore((state) => state.roundData)
  const [videoHasMounted, setVideoHasMounted] = useState<boolean>(false)
  const lastRoundIdRef = useRef<string | null>(null)

  const { betOptionsButtons, chipsButtons, controlsButtons } =
    useControlPanels()

  useEffect(() => {
    const openVideoWindow = () => {
      if (!videoHasMounted) {
        setVideoHasMounted(true)
        const videoWindow = window.open(
          "/video",
          "_blank",
          "width=800,height=600"
        )
        if (videoWindow) {
          videoWindow.moveTo(0, 0)
          videoWindow.resizeTo(screen.width, screen.height)
        }
      }
    }
    openVideoWindow()
  }, [])

  useEffect(() => {
    if (
      !Resulting ||
      !roundData ||
      !roundData.rouletteNumber ||
      !roundData.bonusNumber
    )
      return
    const currentRoundId = roundData.rouletteDrawId?.toString()
    if (!currentRoundId || currentRoundId === lastRoundIdRef.current) return
    lastRoundIdRef.current = currentRoundId
  }, [Resulting, roundData])

  const controlPanelProps = {
    showTooltips: true,
    theme: "default" as const,
    autoSwitch: true,
    useDeviceOrientation: true,
  }

  return (
    <main className='min-h-screen grid grid-cols-[0.9fr_1fr_1.175fr_1fr] overflow-hidden grid-rows-[minmax(0,1.075fr)_minmax(0,2fr)_auto] gap-4 px-5 pt-5'>
      {/* Top row - Game information sections */}
      <GameSection
        hasInsetShadow={false}
        className='w-full h-full col-start-1 row-start-1 '
        title='History'
      >
        <section className='flex h-full flex-col gap-2'>
          {/* Side Menu and Winners Panel */}
          <SideMenu />
          <WinnersPanel />
        </section>
      </GameSection>

      {/* Current Bets component with Past Numbers integration */}
      <GameSection
        style={{
          boxShadow: "1px 1px 0px 0.2px rgba(255, 255, 255, 0.5)",
        }}
        className=' col-start-2 row-start-1 bg-black/40'
        title='Statistics'
      >
        <StatisticsPanel />
      </GameSection>

      <GameSection
        style={{
          boxShadow: "1px 1px 0px 1px rgba(12, 174, 18, 0.75)",
        }}
        className='w-full h-full col-start-3 row-start-1 '
        title='Video'
      >
        <VideoPlayer />
      </GameSection>

      <GameSection
        style={{
          boxShadow: "1px 1px 0px 0.2px rgba(255, 255, 255, 0.5)",
        }}
        className='col-start-4 row-start-1 bg-black/40'
        title='Past Results'
      >
        <PastResultsPanel />
      </GameSection>

      {/* Middle row - Betting areas */}
      <GameSection
        className='bg-black/40 border-2 border-[#c69e61] col-start-1 row-start-2 flex-grow'
        title='Bonus Bets'
      >
        <BonusBetsPanel />
      </GameSection>

      <GameSection
        hasInsetShadow={false}
        className='col-start-2 bg-black/40 row-start-2 col-span-full flex-grow overflow-hidden'
        title='Betting Table'
      >
        <RouletteTable />
      </GameSection>

      {/* Bottom row - Control panels with subgrid */}
      <section className='col-span-full row-start-3'>
        <div className='grid grid-cols-[repeat(24,1fr)] gap-4'>
          <GameSection
            hasInsetShadow={false}
            className='w-full h-auto col-span-9'
            title='Bet Options'
          >
            <ResponsiveControlPanel
              buttons={betOptionsButtons}
              {...controlPanelProps}
            />
          </GameSection>

          <GameSection
            hasInsetShadow={false}
            className='w-full h-auto col-span-6'
            title='Chips'
          >
            <ResponsiveControlPanel
              buttons={chipsButtons}
              zeroGapForChips={true}
              {...controlPanelProps}
            />
          </GameSection>

          <GameSection
            hasInsetShadow={false}
            className='w-full h-auto col-span-9'
            title='Controls'
          >
            <ResponsiveControlPanel
              buttons={controlsButtons}
              {...controlPanelProps}
            />
          </GameSection>
        </div>

        <div className='w-full flex justify-center items-center whitespace-nowrap py-1'>
          <span>Balance: R1,000,000.00</span>
          <Divider className='rotate-90 w-10' />
          <span>Total bet: R0.00</span>
        </div>
      </section>
    </main>
  )
}

export default Retail
