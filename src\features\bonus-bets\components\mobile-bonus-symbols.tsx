import type React from "react"
import { cn } from "@/lib/utils"
import type { SelectableCell } from "@config/selector-config"
import BonusSymbol from "../bonus-symbol"
import { BONUS_PANEL_STYLES } from "../constants/bonus-panel-styles"

interface MobileBonusSymbolsProps {
  betButtons: SelectableCell[]
}

/**
 * Component for displaying bonus symbols in mobile layout
 */
export const MobileBonusSymbols: React.FC<MobileBonusSymbolsProps> = ({ 
  betButtons 
}) => {
  const symbolCount = betButtons.length

  return (
    <>
      <div className='text-center text-[#bf9a5d] font-bold mb-2'>
        BONUS
        <br /> BET
      </div>
      {/* Use grid instead of flex for more precise control over spacing */}
      <div
        className='grid h-full'
        style={{
          gridTemplateRows: `repeat(${symbolCount}, 1fr)`,
          gap: "8px",
        }}
      >
        {betButtons.map((bonus, index) => (
          <div className='flex justify-center items-center' key={index}>
            <BonusSymbol
              bonus={bonus}
              className={cn(BONUS_PANEL_STYLES.symbolSize, "justify-self-center")}
            />
          </div>
        ))}
      </div>
    </>
  )
}
