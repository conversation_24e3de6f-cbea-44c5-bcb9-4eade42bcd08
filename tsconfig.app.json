{
  "compilerOptions": {
    "target": "ESNext",
    "useDefineForClassFields": true,
    "lib": [
      "ESNext",
      "DOM",
      "DOM.Iterable"
    ],
    "module": "ESNext",
    "skipLibCheck": true,
    /* Bundler mode */
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "isolatedModules": true,
    "moduleDetection": "force",
    "noEmit": true,
    "jsx": "react-jsx",
    /* Linting */
    "strict": true,
    "noImplicitAny": false, /* Disable implicit any checks */
    "noUnusedLocals": false, /* Disable unused locals checks */
    "noUnusedParameters": false, /* Disable unused parameters checks */
    "noFallthroughCasesInSwitch": true,
    /* Paths */
    "baseUrl": ".",
    "types": [
      "./node_modules/network-information-types",
      "node"
    ],
    "paths": {
      "@/*": [
        "./src/*"
      ],
      "@components/*": [
        "./src/components/*"
      ],
      "@hooks/*": [
        "./src/hooks/*"
      ],
      "@lib/*": [
        "./src/lib/*"
      ],
      "@middleware/*": [
        "./src/middleware/*"
      ],
      "@stores/*": [
        "./src/stores/*"
      ],
      "@views/*": [
        "./src/views/*"
      ],
      "@config/*": [
        "config/*"
      ],
      "@assets/*": [
        ".//assets/*"
      ]
    },
    "plugins": [
      {
        // Both 'name' (for LSP) and 'transform' (for build) in one entry
        "name": "@maxmorozoff/try-catch-tuple-ts-plugin",
        "transform": "@maxmorozoff/try-catch-tuple-ts-plugin/transformer",
        // --- SHARED Configuration (applies to both LSP & Transformer) ---
        "errorLevel": "error", // or "warning". Default: "error"
        "allowIgnoredError": true, // Default: true
        "checkWrappedCalls": true // Default: true
      }
    ]
  },
  "include": [
    "src",
    "config",
  ]
}