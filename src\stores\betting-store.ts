import { BetTypes, getBetTypes } from "@/hooks/api/use-api"
import { getBetGroup } from "@/hooks/game/use-bet-groups"
import { createStoreLogger } from "@/hooks/logging/use-stable-logger"
import { findLiteralChip } from "@/middleware/game/chipMiddleware"
import {
  createEnhancedStore,
  createSelector,
} from "@/middleware/storage/stateMiddleware"
import { Chip } from "@config/chip-config"
import { SelectableCell } from "@config/selector-config"

// Create a stable logger for this store
const storeLogger = createStoreLogger("BettingStore")

// Constants
const DEFAULT_CHIP: Chip = {
  value: 5,
  alt: "Blue Chip",
  src: "/assets/images/chips/blue-chip.webp",
}

const LIMITS = {
  MIN_CHIP_VALUE: 2,
  MAX_CHIP_VALUE: 1000,
  MAX_CELL_VALUE: 1000,
  INITIAL_BALANCE: 250000,
  MAX_AUTOPLAY_ROUNDS: 100,
  DEFAULT_AUTOPLAY_DELAY: 2000,
}

// Types
interface AutoplayConfig {
  active: boolean
  maxRounds: number
  currentRound: number
  stopOnWin: boolean
  stopOnLoss: boolean
  stopAboveBalance: number | null
  stopBelowBalance: number | null
  delayBetweenRounds: number
}

interface SavedBet {
  name: string
  chips: SelectableCell[]
  totalAmount: number
}

export type BetActionType =
  | "half"
  | "double"
  | "undo"
  | "clear"
  | "repeat-last"
  | "repeat-saved"
  | "save-current"
  | "start-autoplay"
  | "stop-autoplay"
  | "black-split"
  | "red-split"

// Main store interface
interface BettingStore {
  // Chip selection
  selectedChip: Chip | null
  setSelectedChip: (chip: Chip | null) => void

  // Placed chips
  placedChips: SelectableCell[]
  setPlacedChips: (
    updater: SelectableCell[] | ((prev: SelectableCell[]) => SelectableCell[])
  ) => void
  totalBet: number

  // Balance
  balance: number
  setBalance: (balance: number) => void

  // Chip actions
  addChip: (cell: SelectableCell, chip: Chip | null) => void
  canPlaceBet: (betAmount: number) => boolean
  handleBetAction: (actionType: BetActionType, params?: any) => void

  // Bet history
  betHistory: SelectableCell[][]
  addToBetHistory: (bet: SelectableCell[]) => void
  lastPlacedBet: SelectableCell[]

  // Saved bets
  savedBets: SavedBet[]
  currentSavedBetIndex: number | null
  saveBet: (name: string) => void
  selectSavedBet: (index: number | null) => void

  // Autoplay
  autoplay: AutoplayConfig
  updateAutoplayConfig: (config: Partial<AutoplayConfig>) => void
  startAutoplay: () => void
  stopAutoplay: () => void
  processAutoplayRound: (roundResult: { win: boolean; amount: number }) => void
  runAutoplayRound: () => void

  // Messages
  message: string | null
  setMessage: (message: string | null) => void
  showMessage: (message: string, duration?: number) => void

  // Bet types
  betTypes: BetTypes[] | null
  initializeBetTypes: () => Promise<BetTypes[]>

  // Winnings
  winnings: number
  setWinnings: (winnings: number) => void

  // Betslip
  betslipId: number
  setBetslipId: (id: number) => void

  // Round handling
  handleRoundComplete: () => void

  // Favorites
  favoritesPopoverOpen: boolean
  setFavoritesPopoverOpen: (open: boolean) => void
}

// Helper functions
const calculateTotalBet = (chips: SelectableCell[]): number =>
  chips.reduce(
    (sum, cell) =>
      sum +
      (cell.chips_placed?.reduce((chipSum, chip) => chipSum + chip.value, 0) ||
        0),
    0
  )

const cloneBets = (bets: SelectableCell[]): SelectableCell[] =>
  bets.map((cell) => ({
    ...cell,
    chips_placed: [...(cell.chips_placed || [])].map((chip) => ({ ...chip })),
  }))

// Create the enhanced store
export const useBettingStore = createEnhancedStore<BettingStore>(
  (set, get) => ({
    // Favorites
    favoritesPopoverOpen: true,
    setFavoritesPopoverOpen: (open) =>
      set((state) => {
        state.favoritesPopoverOpen = open
      }),

    // Chip selection
    selectedChip: DEFAULT_CHIP,
    setSelectedChip: (chip) =>
      set((state) => {
        state.selectedChip = chip
      }),

    // Placed chips
    placedChips: [],
    setPlacedChips: (updater) =>
      set((state) => {
        const newPlacedChips =
          typeof updater === "function" ? updater(state.placedChips) : updater

        state.placedChips = newPlacedChips
        state.totalBet = calculateTotalBet(newPlacedChips)
      }),

    totalBet: 0,

    // Balance
    balance: LIMITS.INITIAL_BALANCE,
    setBalance: (balance) =>
      set((state) => {
        state.balance = Math.max(0, balance)
      }),

    // Messages
    message: null,
    setMessage: (message) =>
      set((state) => {
        state.message = message
      }),
    showMessage: (message, duration = 3000) => {
      set((state) => {
        state.message = message
      })

      setTimeout(
        () =>
          set((state) => {
            state.message = null
          }),
        duration
      )
    },

    // Bet history
    betHistory: [],
    addToBetHistory: (bet) => {
      set((state) => {
        if (bet.length === 0) return

        // Log the bet being added to history
        logger.debug("Adding bet to history", {
          context: "BettingStore",
          data: {
            betSize: bet.length,
            totalAmount: calculateTotalBet(bet),
          },
        })

        // Add to history, keeping only the last 10 bets
        state.betHistory = [cloneBets(bet), ...state.betHistory].slice(0, 10)
      })
    },
    lastPlacedBet: [],

    // Saved bets
    savedBets: [],
    currentSavedBetIndex: null,
    saveBet: (name) =>
      set((state) => {
        const { placedChips } = state
        if (!placedChips.length) return

        const newSavedBet: SavedBet = {
          name,
          chips: cloneBets(placedChips),
          totalAmount: calculateTotalBet(placedChips),
        }

        // Log the saved bet
        logger.info("Bet saved", {
          context: "BettingStore",
          data: {
            name,
            chipCount: placedChips.length,
            totalAmount: newSavedBet.totalAmount,
          },
        })

        state.savedBets.push(newSavedBet)
      }),
    selectSavedBet: (index) =>
      set((state) => {
        state.currentSavedBetIndex = index
      }),

    // Autoplay
    autoplay: {
      active: false,
      maxRounds: 999999, // Set to very high number for essentially unlimited rounds
      currentRound: 0,
      stopOnWin: false,
      stopOnLoss: false,
      stopAboveBalance: null,
      stopBelowBalance: null,
      delayBetweenRounds: LIMITS.DEFAULT_AUTOPLAY_DELAY,
    },
    updateAutoplayConfig: (config) =>
      set((state) => {
        Object.assign(state.autoplay, config)
      }),

    startAutoplay: () => {
      // Measure autoplay performance
      const state = get()

      // We need to save the bet for autoplay but not actually place it yet
      let autoBet: SelectableCell[] = []

      if (state.placedChips.length > 0) {
        // If there's already a bet placed, use that one
        autoBet = cloneBets(state.placedChips)
      } else if (
        state.betHistory.length > 0 ||
        state.lastPlacedBet.length > 0
      ) {
        // Otherwise, try to use the last bet from history or lastPlacedBet
        autoBet =
          state.betHistory.length > 0
            ? cloneBets(state.betHistory[0])
            : cloneBets(state.lastPlacedBet)

        const totalCost = calculateTotalBet(autoBet)
        if (!state.canPlaceBet(totalCost)) {
          state.showMessage("Insufficient balance for autoplay")

          // Log the error
          logger.warn("Insufficient balance for autoplay", {
            context: "Autoplay",
            data: {
              balance: state.balance,
              requiredAmount: totalCost,
            },
          })

          return
        }
      } else {
        state.showMessage("No bet to repeat for autoplay")

        // Log the error
        logger.warn("No bet to repeat for autoplay", { context: "Autoplay" })
        return
      }

      // Store the saved bet for autoplay without placing it yet
      set((state) => {
        state.autoplay.active = true
        state.autoplay.currentRound = 0 // Start at 0, will be incremented before first run
        state.lastPlacedBet = autoBet // Save the bet to be used in runAutoplayRound
      })

      state.showMessage("Autoplay started")

      // Log autoplay start
      logger.info("Autoplay started", {
        context: "Autoplay",
        data: {
          betCount: autoBet.length,
          totalAmount: calculateTotalBet(autoBet),
        },
      })
    },

    stopAutoplay: () => {
      const state = get()
      if (state.autoplay.active) {
        set((state) => {
          state.autoplay.active = false
          state.autoplay.currentRound = 0
        })

        state.showMessage("Autoplay stopped")

        // Log autoplay stop
        logger.info("Autoplay stopped", {
          context: "Autoplay",
          data: {
            completedRounds: state.autoplay.currentRound,
          },
        })
      }
    },

    runAutoplayRound: () => {
      // Measure autoplay round performance
      const state = get()
      if (!state.autoplay.active) return

      // Check if we're already in a betting phase (placedChips is not empty)
      // If so, we don't want to place another bet
      if (state.placedChips.length > 0) {
        return
      }

      // If this is our first round, increment the counter
      if (state.autoplay.currentRound === 0) {
        set((state) => {
          state.autoplay.currentRound += 1
        })
      }

      // Use the last placed bet
      const lastBet = state.lastPlacedBet
      const betAmount = calculateTotalBet(lastBet)

      // Only stop if we literally cannot place the bet
      if (state.balance < betAmount) {
        state.showMessage("Autoplay stopped: Insufficient balance")
        state.stopAutoplay()

        // Log the error
        logger.warn("Autoplay stopped: Insufficient balance", {
          context: "Autoplay",
          data: {
            balance: state.balance,
            requiredAmount: betAmount,
          },
        })

        return
      }

      // Place the bet by setting placedChips and adjusting balance
      set((state) => {
        state.placedChips = cloneBets(lastBet)
        state.balance -= betAmount
        state.totalBet = betAmount
      })

      // Log the autoplay round
      storeLogger.debug("Autoplay round placed bet", {
        round: state.autoplay.currentRound,
        betAmount,
        remainingBalance: state.balance,
      })
    },

    processAutoplayRound: (roundResult) => {
      const state = get()
      if (!state.autoplay.active) return

      const { win, amount } = roundResult

      // Update balance with winnings if any
      if (win && amount > 0) {
        set((state) => {
          state.balance += amount
        })

        // Log the win
        storeLogger.info("Autoplay round win", {
          round: state.autoplay.currentRound,
          winAmount: amount,
          newBalance: state.balance + amount,
        })
      } else {
        // Log the loss
        storeLogger.debug("Autoplay round loss", {
          round: state.autoplay.currentRound,
        })
      }
    },

    // Bet types
    betTypes: null,
    initializeBetTypes: async (): Promise<BetTypes[]> => {
      // Measure initialization performance
      try {
        const types = await getBetTypes()

        set((state) => {
          state.betTypes = types
        })

        // Log success
        storeLogger.info("Bet types initialized", {
          typeCount: types?.length || 0,
        })

        return types
      } catch (error) {
        // Log error
        storeLogger.error("Failed to initialize bet types", error)
        throw error
      }
    },

    // Winnings
    winnings: 0,
    setWinnings: (winnings) =>
      set((state) => {
        state.winnings = winnings
      }),

    // Betslip
    betslipId: 0,
    setBetslipId: (id) =>
      set((state) => {
        state.betslipId = id
      }),

    // Chip actions
    addChip: (cell, chip) => {
      // Measure chip addition performance
      set((state) => {
        if (!chip) return
        if (state.balance < chip.value) return

        const newPlacedChips = [...state.placedChips]
        const existingCellIndex = newPlacedChips.findIndex(
          (c) => c.cell_id === cell.cell_id
        )

        const safeCell = {
          ...cell,
          chips_placed: cell.chips_placed || [],
        }

        if (existingCellIndex >= 0) {
          const existingCell = newPlacedChips[existingCellIndex]
          const currentTotal = existingCell.total_chip_value || 0

          if (currentTotal + chip.value > LIMITS.MAX_CELL_VALUE) return

          const betTypeId =
            existingCell.bet_type_id ||
            getBetGroup(existingCell.cell_selectors, state.betTypes || [])

          // Consolidate chips into a single chip with total value
          const totalValue = currentTotal + chip.value
          const consolidatedChip = findLiteralChip(totalValue)

          if (!consolidatedChip) return

          newPlacedChips[existingCellIndex] = {
            ...existingCell,
            chips_placed: [consolidatedChip],
            total_chip_value: totalValue,
            bet_type_id: betTypeId,
          }
        } else {
          const betTypeId =
            safeCell.bet_type_id ||
            getBetGroup(safeCell.cell_selectors, state.betTypes || [])

          newPlacedChips.push({
            ...safeCell,
            chips_placed: [chip],
            total_chip_value: chip.value,
            bet_type_id: betTypeId,
          })
        }

        // Update state
        state.placedChips = newPlacedChips
        state.balance -= chip.value
        state.totalBet = calculateTotalBet(newPlacedChips)
        state.lastPlacedBet = cloneBets(newPlacedChips)

        // Log chip placement
        storeLogger.debug("Chip placed", {
          cellId: cell.cell_id,
          chipValue: chip.value,
          totalBet: state.totalBet,
        })
      })
    },

    canPlaceBet: (betAmount) => {
      const { balance } = get()
      return balance >= betAmount
    },

    handleBetAction: (actionType, params) => {
      // Measure bet action performance
      const state = get()

      // Log the action
      storeLogger.debug(`Bet action: ${actionType}`, { params })

      switch (actionType) {
        case "clear": {
          if (state.placedChips.length === 0) return

          state.addToBetHistory(state.placedChips)

          set((state) => {
            state.lastPlacedBet = cloneBets(state.placedChips)
            state.placedChips = []
            state.totalBet = 0
            state.balance += state.totalBet
          })
          break
        }

        case "half": {
          if (state.placedChips.length === 0) return

          set((state) => {
            const newPlacedChips = state.placedChips.map((cell) => {
              const safeChipsPlaced = cell.chips_placed || []

              const newChips = safeChipsPlaced.map((chip) => ({
                ...chip,
                value: Math.max(
                  LIMITS.MIN_CHIP_VALUE,
                  Math.floor(chip.value / 2)
                ),
              }))

              const newTotal = newChips.reduce(
                (sum, chip) => sum + chip.value,
                0
              )

              return {
                ...cell,
                chips_placed: newChips,
                total_chip_value: newTotal,
              }
            })

            const newTotalBet = calculateTotalBet(newPlacedChips)
            const removedBet = state.totalBet - newTotalBet

            state.placedChips = newPlacedChips
            state.balance += removedBet
            state.totalBet = newTotalBet
          })
          break
        }

        case "double": {
          if (state.placedChips.length === 0) return

          const currentTotalBet = state.totalBet
          if (state.balance < currentTotalBet || currentTotalBet <= 0) return

          set((state) => {
            const newPlacedChips = state.placedChips.map((cell) => {
              const safeChipsPlaced = cell.chips_placed || []

              const doubledChips = safeChipsPlaced.map((chip) => ({
                ...chip,
                value: Math.min(LIMITS.MAX_CHIP_VALUE, chip.value * 2),
              }))

              const newTotal = doubledChips.reduce(
                (sum, chip) => sum + chip.value,
                0
              )
              if (newTotal > LIMITS.MAX_CELL_VALUE) return cell

              return {
                ...cell,
                chips_placed: doubledChips,
                total_chip_value: newTotal,
              }
            })

            const newTotalBet = calculateTotalBet(newPlacedChips)
            const actualBetIncrease = newTotalBet - currentTotalBet

            if (state.balance >= actualBetIncrease) {
              state.placedChips = newPlacedChips
              state.balance -= actualBetIncrease
              state.totalBet = newTotalBet
            }
          })
          break
        }

        case "undo": {
          set((state) => {
            const newPlacedChips = [...state.placedChips]
            if (newPlacedChips.length === 0) return

            const lastPlacedCellIndex = newPlacedChips.length - 1
            const lastPlacedCell = newPlacedChips[lastPlacedCellIndex]

            if (
              !lastPlacedCell.chips_placed ||
              lastPlacedCell.chips_placed.length === 0
            ) {
              newPlacedChips.pop()
            } else {
              const removedChip = lastPlacedCell.chips_placed.pop()
              if (removedChip) {
                lastPlacedCell.total_chip_value =
                  (lastPlacedCell.total_chip_value || 0) - removedChip.value

                if (!lastPlacedCell.chips_placed.length) {
                  newPlacedChips.pop()
                }

                state.placedChips = newPlacedChips
                state.balance += removedChip.value
                state.totalBet = calculateTotalBet(newPlacedChips)
              }
            }
          })
          break
        }

        case "repeat-saved": {
          const { savedBets, currentSavedBetIndex } = state
          if (!savedBets.length) return

          const betIndex =
            currentSavedBetIndex !== null ? currentSavedBetIndex : 0

          const savedBet = savedBets[betIndex]
          if (!savedBet) return

          const totalCost = savedBet.totalAmount
          if (!state.canPlaceBet(totalCost)) {
            state.showMessage("Insufficient balance for this bet")
            return
          }

          set((state) => {
            state.placedChips = cloneBets(savedBet.chips)
            state.balance -= totalCost
            state.totalBet = totalCost
          })
          break
        }

        case "repeat-last": {
          // If no previous bet exists, return early
          if (
            state.betHistory.length === 0 &&
            (!state.lastPlacedBet || state.lastPlacedBet.length === 0)
          ) {
            return
          }

          // If there are already chips placed, don't repeat
          if (state.placedChips.length > 0) {
            return
          }

          // Get the bet to repeat
          const betToRepeat =
            state.betHistory.length > 0
              ? cloneBets(state.betHistory[0])
              : cloneBets(state.lastPlacedBet)

          const totalCost = calculateTotalBet(betToRepeat)

          if (!state.canPlaceBet(totalCost)) {
            state.showMessage("Insufficient balance for this bet")
            return
          }

          // Place the repeated bet
          set((state) => {
            state.placedChips = betToRepeat
            state.balance -= totalCost
            state.totalBet = totalCost
          })

          break
        }

        case "save-current": {
          if (state.placedChips.length === 0) {
            state.showMessage("No bet to save")
            return
          }

          const name = params?.name || `Bet ${state.savedBets.length + 1}`
          state.saveBet(name)
          state.showMessage(`Bet saved as "${name}"`)
          break
        }

        case "start-autoplay": {
          if (state.autoplay.active) {
            state.stopAutoplay()
          } else {
            state.startAutoplay()
          }
          break
        }

        case "stop-autoplay": {
          state.stopAutoplay()
          break
        }

        case "black-split": {
          // Place bets on black splits
          if (!state.selectedChip) {
            state.showMessage("Please select a chip first")
            return
          }

          // Import the necessary configurations
          import("@config/selector-config").then(({ blackCell }) => {
            if (blackCell && blackCell.cell_selectors) {
              // Place a bet on the black cell
              const chip = state.selectedChip
              if (chip) {
                state.addChip(blackCell, chip)
                storeLogger.debug("Black split bet placed", {
                  chipValue: chip.value,
                })
              }
            }
          })
          break
        }

        case "red-split": {
          // Place bets on red splits
          if (!state.selectedChip) {
            state.showMessage("Please select a chip first")
            return
          }

          // Import the necessary configurations
          import("@config/selector-config").then(({ redCell }) => {
            if (redCell && redCell.cell_selectors) {
              // Place a bet on the red cell
              const chip = state.selectedChip
              if (chip) {
                state.addChip(redCell, chip)
                storeLogger.debug("Red split bet placed", {
                  chipValue: chip.value,
                })
              }
            }
          })
          break
        }
      }
    },

    handleRoundComplete: () => {
      // Measure round completion performance
      const state = get()

      if (state.placedChips.length > 0) {
        // Save the current bet to history
        state.addToBetHistory(state.placedChips)

        // Update state
        set((state) => {
          // If autoplay is active, update the lastPlacedBet to use the current chips
          // This ensures that any chips added during autoplay are included in future rounds
          state.lastPlacedBet = cloneBets(state.placedChips)
          state.placedChips = [] // Clear the placed chips after a round is complete
        })

        // Log round completion
        storeLogger.debug("Round completed", {
          isAutoplay: state.autoplay.active,
          totalBet: state.totalBet,
        })
      }

      // For autoplay, we need to check if we should continue to the next round
      if (state.autoplay.active) {
        // Increment the round counter for the next round
        set((state) => {
          state.autoplay.currentRound += 1
        })

        // Only check balance condition - allow unlimited rounds otherwise
        const { stopBelowBalance } = state.autoplay

        // Only stop if explicitly configured to stop below a balance threshold
        // and that threshold has been reached
        const shouldStop =
          stopBelowBalance !== null && state.balance < stopBelowBalance

        if (shouldStop) {
          state.showMessage(`Autoplay stopped: Balance below threshold`)
          state.stopAutoplay()

          // Log autoplay stop
          storeLogger.info("Autoplay stopped: Balance below threshold", {
            balance: state.balance,
            threshold: stopBelowBalance,
          })
        } else {
          // Schedule the next autoplay round with a delay
          setTimeout(
            () => get().runAutoplayRound(),
            state.autoplay.delayBetweenRounds
          )

          // Log next round scheduled
          logger.debug("Next autoplay round scheduled", {
            context: "Autoplay",
            data: {
              nextRound: state.autoplay.currentRound + 1,
              delay: state.autoplay.delayBetweenRounds,
            },
          })
        }
      }
    },
  }),
  {
    name: "enhanced-betting-storage",
    persist: true,
    secure: true,
    immer: true,
    partialize: (state): Partial<BettingStore> => ({
      balance: state.balance,
      betTypes: state.betTypes,
      savedBets: state.savedBets,
      betHistory: state.betHistory,
      lastPlacedBet: state.lastPlacedBet,
      autoplay: {
        maxRounds: state.autoplay.maxRounds,
        stopOnWin: state.autoplay.stopOnWin,
        stopOnLoss: state.autoplay.stopOnLoss,
        stopAboveBalance: state.autoplay.stopAboveBalance,
        stopBelowBalance: state.autoplay.stopBelowBalance,
        delayBetweenRounds: state.autoplay.delayBetweenRounds,
        active: false,
        currentRound: 0,
      },
    }),
  }
)

// Create selectors for common state values
export const useSelectedChip = createSelector(
  useBettingStore,
  (state) => state.selectedChip
)

export const usePlacedChips = createSelector(
  useBettingStore,
  (state) => state.placedChips
)

export const useTotalBet = createSelector(
  useBettingStore,
  (state) => state.totalBet
)

export const useBalance = createSelector(
  useBettingStore,
  (state) => state.balance
)

export const useBetHistory = createSelector(
  useBettingStore,
  (state) => state.betHistory
)

export const useSavedBets = createSelector(
  useBettingStore,
  (state) => state.savedBets
)

export const useAutoplay = createSelector(
  useBettingStore,
  (state) => state.autoplay
)

export const useBetTypes = createSelector(
  useBettingStore,
  (state) => state.betTypes
)

export const useWinnings = createSelector(
  useBettingStore,
  (state) => state.winnings
)

export const useBetslipId = createSelector(
  useBettingStore,
  (state) => state.betslipId
)

// Composite selectors
export const useBettingActions = createSelector(useBettingStore, (state) => ({
  addChip: state.addChip,
  handleBetAction: state.handleBetAction,
  canPlaceBet: state.canPlaceBet,
  setSelectedChip: state.setSelectedChip,
}))

export const useAutoplayActions = createSelector(useBettingStore, (state) => ({
  startAutoplay: state.startAutoplay,
  stopAutoplay: state.stopAutoplay,
  updateAutoplayConfig: state.updateAutoplayConfig,
}))
