import { createEnhancedStore, createSelector } from "@/middleware"
import { createStoreLogger } from "@/hooks/logging/use-stable-logger"
import { SoundKeys, soundMap } from "@config/audio-config"

export const QUALITY_HEIGHT_MAP = {
  auto: -1,
  "1080p": 1080,
  "720p": 720,
  "480p": 480,
  "360p": 360,
} as const

export type VideoQuality = keyof typeof QUALITY_HEIGHT_MAP
export type AudioType = "sfx" | "music"
export type DealerVoice = "RSA" | "UK" | "US" | "ZULU" | "XHOSA"

interface SettingsState {
  // Audio settings
  isMuted: boolean
  masterVolume: number
  sfxVolume: number
  musicVolume: number
  dealerVoice: DealerVoice

  // Video settings
  isVideoDisplayed: boolean
  videoQuality: VideoQuality

  // Game settings
  initialNeighbours: number

  // Sound management with proper cleanup
  activeSounds: Set<HTMLAudioElement>
  sounds: Record<string, HTMLAudioElement>
  audioPool: HTMLAudioElement[]
  maxAudioInstances: number

  // Audio functions
  playSound: (
    key: SoundKeys,
    loop?: boolean,
    preventDuplicates?: boolean
  ) => Promise<void>
  preloadSounds: (
    soundMap: Record<string, { type: AudioType; src: string }>
  ) => void

  // Actions
  setIsMuted: (isMuted: boolean) => void
  setMasterVolume: (volume: number) => void
  setSfxVolume: (volume: number) => void
  setMusicVolume: (volume: number) => void
  setDealerVoice: (voice: DealerVoice) => void
  setIsVideoDisplayed: (isDisplayed: boolean) => void
  setVideoQuality: (quality: VideoQuality) => void
  setInitialNeighbours: (neighbours: number) => void
  resetSettings: () => void
  cleanupAudioResources: () => void
}

// Audio management constants
const AUDIO_LIMITS = {
  maxInstances: 10, // Prevent WebMediaPlayer limit exceeded
  poolSize: 5, // Reusable audio elements
} as const

// Default settings
const DEFAULT_SETTINGS = {
  isMuted: false,
  masterVolume: 0.7,
  sfxVolume: 0.8,
  musicVolume: 0.5,
  dealerVoice: "RSA" as DealerVoice,
  isVideoDisplayed: import.meta.env.VITE_APP_BUILD === "online" ? true : false,
  videoQuality: "auto" as VideoQuality,
  initialNeighbours: 1,
}

// Create a stable logger for this store
const storeLogger = createStoreLogger("SettingsStore")

// Log default settings
storeLogger.debug("Initializing settings store with defaults", DEFAULT_SETTINGS)

// Create the enhanced store
export const useSettingsStore = createEnhancedStore<SettingsState>(
  (set, get) => ({
    // Initial state
    ...DEFAULT_SETTINGS,

    // Sound management with proper limits and cleanup
    activeSounds: new Set<HTMLAudioElement>(),
    sounds: {},
    audioPool: [],
    maxAudioInstances: AUDIO_LIMITS.maxInstances,

    // Audio functions with proper resource management
    preloadSounds: (
      soundMap: Record<string, { type: AudioType; src: string }>
    ) => {
      storeLogger.info("Preloading sounds with resource management", {
        count: Object.keys(soundMap).length,
      })

      // Clean up existing audio resources first
      const { sounds, activeSounds } = get()

      // Stop and cleanup existing sounds
      activeSounds.forEach((audio) => {
        audio.pause()
        audio.currentTime = 0
        audio.removeEventListener("ended", () => {})
      })

      // Clear existing sounds
      Object.values(sounds).forEach((audio) => {
        audio.pause()
        audio.src = ""
        audio.load() // Force cleanup
      })

      const audioElements: Record<string, HTMLAudioElement> = {}

      for (const [key, { type, src }] of Object.entries(soundMap)) {
        try {
          const audio = new Audio()
          audio.preload = "metadata" // Only load metadata initially
          audio.dataset.type = type
          audio.src = src
          audioElements[key] = audio

          // Add ended event listener to remove from activeSounds
          const handleEnded = () => {
            if (!audio.loop) {
              set((state) => {
                state.activeSounds.delete(audio)
              })
              storeLogger.debug("Sound ended and removed from active sounds", {
                key,
              })
            }
          }

          audio.addEventListener("ended", handleEnded)

          // Store cleanup function for later use
          audio.dataset.cleanup = "true"
        } catch (error) {
          storeLogger.error(`Error preloading sound: ${key}`, error, { key })
        }
      }

      set((state) => {
        state.sounds = audioElements
        state.activeSounds = new Set() // Reset active sounds
      })
    },

    playSound: async (
      key: SoundKeys,
      loop = false,
      preventDuplicates = true
    ) => {
      const {
        sounds,
        isMuted,
        masterVolume,
        sfxVolume,
        musicVolume,
        activeSounds,
        maxAudioInstances,
      } = get()

      if (!sounds[key]) {
        storeLogger.warn(`Sound not found: ${key}`, { key })
        return Promise.resolve()
      }

      // Check audio instance limit to prevent WebMediaPlayer exceeded error
      if (activeSounds.size >= maxAudioInstances) {
        storeLogger.warn(
          `Audio instance limit reached (${maxAudioInstances}), skipping sound: ${key}`,
          { key, maxAudioInstances }
        )
        return Promise.resolve()
      }

      const audio = sounds[key]

      try {
        if (preventDuplicates) {
          // Find and stop any existing instances of this sound
          const soundsToRemove: HTMLAudioElement[] = []
          activeSounds.forEach((activeAudio) => {
            if (activeAudio.src === audio.src) {
              storeLogger.debug(`Stopping duplicate sound: ${key}`, { key })
              activeAudio.pause()
              activeAudio.currentTime = 0
              soundsToRemove.push(activeAudio)
            }
          })

          // Update state to remove stopped sounds
          if (soundsToRemove.length > 0) {
            set((state) => {
              soundsToRemove.forEach((sound) =>
                state.activeSounds.delete(sound)
              )
            })
          }
        }

        const type = audio.dataset.type as AudioType
        const volume = isMuted
          ? 0
          : type === "sfx"
          ? masterVolume * sfxVolume
          : masterVolume * musicVolume

        audio.volume = volume
        audio.loop = loop
        audio.currentTime = 0

        storeLogger.debug(`Playing sound: ${key}`, {
          key,
          volume,
          loop,
          activeCount: activeSounds.size,
        })

        return new Promise<void>((resolve) => {
          audio.onended = () => resolve()
          audio.play().catch((error) => {
            storeLogger.error(`Error playing sound: ${key}`, error, { key })
            resolve()
          })

          // Add to active sounds using Immer's draft state
          set((state) => {
            state.activeSounds.add(audio)
          })
        })
      } catch (error) {
        storeLogger.error(`Error playing sound: ${key}`, error, { key })
        return Promise.resolve()
      }
    },

    // Actions
    setIsMuted: (isMuted) =>
      set((state) => {
        state.isMuted = isMuted

        // Update all active sounds when mute state changes
        const { activeSounds } = get()
        activeSounds.forEach((audio) => {
          if (isMuted) {
            audio.volume = 0
          } else {
            const { masterVolume, sfxVolume, musicVolume } = get()
            const type = audio.dataset.type as AudioType
            audio.volume =
              type === "sfx"
                ? masterVolume * sfxVolume
                : masterVolume * musicVolume
          }
        })

        storeLogger.debug("Settings updated: isMuted", { isMuted })
      }),

    setMasterVolume: (volume) =>
      set((state) => {
        const newVolume = Math.max(0, Math.min(1, volume))
        state.masterVolume = newVolume

        // Update volume for active sounds
        if (!state.isMuted) {
          const { activeSounds } = get()
          activeSounds.forEach((audio) => {
            const type = audio.dataset.type as AudioType
            audio.volume =
              type === "sfx"
                ? newVolume * state.sfxVolume
                : newVolume * state.musicVolume
          })
        }

        storeLogger.debug("Settings updated: masterVolume", {
          masterVolume: newVolume,
        })
      }),

    setSfxVolume: (volume) =>
      set((state) => {
        const newVolume = Math.max(0, Math.min(1, volume))
        state.sfxVolume = newVolume

        // Update volume for active SFX sounds
        if (!state.isMuted) {
          const { activeSounds, masterVolume } = get()
          activeSounds.forEach((audio) => {
            if (audio.dataset.type === "sfx") {
              audio.volume = masterVolume * newVolume
            }
          })
        }

        storeLogger.debug("Settings updated: sfxVolume", {
          sfxVolume: newVolume,
        })
      }),

    setMusicVolume: (volume) =>
      set((state) => {
        const newVolume = Math.max(0, Math.min(1, volume))
        state.musicVolume = newVolume

        // Update volume for active music sounds
        if (!state.isMuted) {
          const { activeSounds, masterVolume } = get()
          activeSounds.forEach((audio) => {
            if (audio.dataset.type === "music") {
              audio.volume = masterVolume * newVolume
            }
          })
        }

        storeLogger.debug("Settings updated: musicVolume", {
          musicVolume: newVolume,
        })
      }),

    setDealerVoice: (voice) =>
      set((state) => {
        state.dealerVoice = voice
        storeLogger.debug("Settings updated: dealerVoice", {
          dealerVoice: voice,
        })
      }),

    setIsVideoDisplayed: (isDisplayed) =>
      set((state) => {
        state.isVideoDisplayed = isDisplayed
        storeLogger.debug("Settings updated: isVideoDisplayed", {
          isVideoDisplayed: isDisplayed,
        })
      }),

    setVideoQuality: (quality) =>
      set((state) => {
        state.videoQuality = quality
        storeLogger.debug("Settings updated: videoQuality", {
          videoQuality: quality,
        })
      }),

    setInitialNeighbours: (neighbours) =>
      set((state) => {
        state.initialNeighbours = Math.min(9, Math.max(1, neighbours))
        storeLogger.debug("Settings updated: initialNeighbours", {
          initialNeighbours: state.initialNeighbours,
        })
      }),

    resetSettings: () =>
      set((state) => {
        // Stop all active sounds
        const { activeSounds } = get()
        activeSounds.forEach((audio) => {
          audio.pause()
          audio.currentTime = 0
        })

        // Clear active sounds
        state.activeSounds = new Set()

        // Clear the localStorage entry for this store
        try {
          localStorage.removeItem("enhanced-settings-storage")
          storeLogger.debug("Settings storage cleared")
        } catch (error) {
          storeLogger.error("Failed to clear settings storage", error)
        }

        // Reset to default settings
        Object.assign(state, DEFAULT_SETTINGS)

        storeLogger.debug("Settings reset to defaults", DEFAULT_SETTINGS)
      }),

    cleanupAudioResources: () =>
      set((state) => {
        const { activeSounds, sounds } = get()

        storeLogger.info("Cleaning up audio resources", {
          activeCount: activeSounds.size,
          totalSounds: Object.keys(sounds).length,
        })

        // Stop and cleanup all active sounds
        activeSounds.forEach((audio) => {
          audio.pause()
          audio.currentTime = 0
          audio.src = ""
          audio.load() // Force cleanup
        })

        // Clear all sound references
        Object.values(sounds).forEach((audio) => {
          audio.pause()
          audio.src = ""
          audio.load()
        })

        // Reset state
        state.activeSounds = new Set()
        state.sounds = {}
        state.audioPool = []

        storeLogger.debug("Audio resources cleaned up")
      }),
  }),
  {
    name: "enhanced-settings-storage",
    persist: true,
    secure: true,
    immer: true,
    onRehydrateStorage: () => {
      return (rehydratedState, error) => {
        if (error) {
          storeLogger.error("Failed to rehydrate settings store", error)
        } else {
          storeLogger.debug("Settings store rehydrated", rehydratedState)
        }
      }
    },
  }
)

// Create selectors for common state values
export const useIsMuted = createSelector(
  useSettingsStore,
  (state) => state.isMuted
)

export const useVideoSettings = createSelector(useSettingsStore, (state) => ({
  isVideoDisplayed: state.isVideoDisplayed,
  videoQuality: state.videoQuality,
}))

export const useAudioSettings = createSelector(useSettingsStore, (state) => ({
  isMuted: state.isMuted,
  masterVolume: state.masterVolume,
  sfxVolume: state.sfxVolume,
  musicVolume: state.musicVolume,
  dealerVoice: state.dealerVoice,
}))

// Sound control selectors
export const useSoundControls = createSelector(useSettingsStore, (state) => ({
  playSound: state.playSound,
  preloadSounds: state.preloadSounds,
}))

// Game settings selectors
export const useGameSettings = createSelector(useSettingsStore, (state) => ({
  initialNeighbours: state.initialNeighbours,
  setInitialNeighbours: state.setInitialNeighbours,
}))

// Reset settings selector
export const useResetSettings = createSelector(
  useSettingsStore,
  (state) => state.resetSettings
)
