export type Chip = {
  value: number
  alt: string
  src: string
}

export const rouletteChips: Chip[] = [
  { value: 2, alt: "Black Chip", src: "/assets/images/chips/black-chip.webp" },
  { value: 5, alt: "Blue Chip", src: "/assets/images/chips/blue-chip.webp" },
  { value: 10, alt: "Green Chip", src: "/assets/images/chips/green-chip.webp" },
  {
    value: 20,
    alt: "Purple Chip",
    src: "/assets/images/chips/purple-chip.webp",
  },
  { value: 50, alt: "Red Chip", src: "/assets/images/chips/red-chip.webp" },
  {
    value: 250,
    alt: "Turqoise Chip",
    src: "/assets/images/chips/turquoise-chip.webp",
  },
  {
    value: 1000,
    alt: "Yellow Chip",
    src: "/assets/images/chips/yellow-chip.webp",
  },
]
