import path from "path"
import tailwindcss from "@tailwindcss/vite"
import react from "@vitejs/plugin-react-swc"
import { defineConfig, loadEnv } from "vite"

export default defineConfig(({ command, mode }) => {
  // Load environment variables based on mode
  const env = loadEnv(mode, process.cwd(), "")

  // Determine if we're in development
  const isDev = command === "serve"
  const isProduction = mode === "production" || mode === "prod"

  return {
    // Define global variables to help with SES compatibility
    define: {
      // Ensure these globals are properly defined
      "globalThis.process.env.NODE_ENV": JSON.stringify(
        process.env.NODE_ENV || (isProduction ? "production" : "development")
      ),
      "process.env.NODE_ENV": JSON.stringify(
        process.env.NODE_ENV || (isProduction ? "production" : "development")
      ),
    },
    plugins: [
      tailwindcss(),
      react({
        plugins: [],
      }),
    ],

    // Environment-specific configuration
    envDir: process.cwd(),
    envPrefix: "VITE_",

    build: {
      target: "esnext",
      rollupOptions: {
        output: {
          // Optimize chunk size
          // Ensure consistent chunk naming for better caching
          entryFileNames: "assets/[name]-[hash].js",
          chunkFileNames: "assets/[name]-[hash].js",
          assetFileNames: "assets/[name]-[hash].[ext]",
        },
      },
      // Enable source maps based on environment
      sourcemap: env.VITE_APP_ENABLE_SOURCE_MAPS === "true" || isDev,
      // Minify the output using esbuild (faster than terser)
      minify: isProduction ? "esbuild" : false,
      // Optimize for development or production
      ...(isDev && {
        // Development optimizations
        target: "esnext",
        minify: false,
      }),
      ...(isProduction && {
        // Production optimizations
        target: ["es2020", "edge88", "firefox78", "chrome87", "safari13.1"],
        minify: "esbuild",
        cssMinify: true,
      }),
    },

    // Development server configuration
    server: {
      port: 3000,
      host: true,
      open: false, // Disabled - we handle browser opening via custom script
      hmr: {
        overlay: isDev,
      },
    },

    // Preview server configuration
    preview: {
      port: 4173,
      host: true,
    },

    resolve: {
      alias: {
        "@": path.resolve(__dirname, "./src"),
        "@components": path.resolve(__dirname, "./src/components"),
        "@hooks": path.resolve(__dirname, "./src/hooks"),
        "@lib": path.resolve(__dirname, "./src/lib"),
        "@middleware": path.resolve(__dirname, "./src/middleware"),
        "@stores": path.resolve(__dirname, "./src/stores"),
        "@views": path.resolve(__dirname, "./src/views"),
        "@config": path.resolve(__dirname, "./config"),
        "@assets": path.resolve(__dirname, "./assets"),
      },
    },

    // Optimizations
    optimizeDeps: {
      include: [
        "react",
        "react-dom",
        "react-router-dom",
        "zustand",
        "immer",
        "@tanstack/react-query",
      ],
    },
  }
})
