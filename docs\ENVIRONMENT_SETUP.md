# Environment Setup Guide

This guide explains how to set up and manage different environments for the Roulette application.

## Quick Start

1. **Copy an environment file for local development:**
   ```bash
   node scripts/env-setup.js copy stg
   ```

2. **Start development server:**
   ```bash
   pnpm dev
   ```

## Environment Files

| Environment | File | Description |
|-------------|------|-------------|
| Local | `.env.local` | Local development (not committed) |
| Staging | `.env.stg` | Staging environment |
| Production | `.env.prod` | Production environment |
| Example | `.env.example` | Template with all variables |

## Available Scripts

### Development Scripts

| Script | Environment | Browser | Description |
|--------|-------------|---------|-------------|
| `pnpm dev` | Local | Chrome | Default development |
| `pnpm dev:local` | Local | Chrome | Local development |
| `pnpm dev:stg` | Staging | Chrome | Staging environment |
| `pnpm dev:prod` | Production | Chrome | Production environment |

### Browser-Specific Development

| Script | Browser | Environment |
|--------|---------|-------------|
| `pnpm dev:chrome` | Chrome | Local |
| `pnpm dev:firefox` | Firefox | Local |
| `pnpm dev:edge` | Edge | Local |
| `pnpm dev:zen` | Zen Browser | Local |
| `pnpm dev:chrome:stg` | Chrome | Staging |
| `pnpm dev:firefox:stg` | Firefox | Staging |

### Build Scripts

| Script | Environment | Description |
|--------|-------------|-------------|
| `pnpm build` | Production | Production build |
| `pnpm build:local` | Local | Local build |
| `pnpm build:stg` | Staging | Staging build |
| `pnpm build:prod` | Production | Production build |

### Preview Scripts

| Script | Environment | Description |
|--------|-------------|-------------|
| `pnpm preview` | Production | Preview production build |
| `pnpm preview:local` | Local | Preview local build |
| `pnpm preview:stg` | Staging | Preview staging build |

## Environment Variables

### Required Variables

- `VITE_APP_API_URL` - Main API endpoint
- `VITE_APP_SIDE_BASE_URL` - Media/streaming base URL
- `VITE_APP_ENV` - Environment type (development/staging/production)
- `VITE_APP_BUILD` - Build type (online/retail/video)

### Optional Variables

- `VITE_APP_DEBUG` - Enable debug mode
- `VITE_APP_MOCK_API` - Use mock API responses
- `VITE_APP_ENABLE_DEVTOOLS` - Enable development tools
- `VITE_APP_ENABLE_ANALYTICS` - Enable analytics tracking
- `VITE_APP_LOG_LEVEL` - Logging level (debug/info/warn/error)
- `VITE_APP_ENABLE_SOURCE_MAPS` - Enable source maps in build

## Environment Management

### Using the Setup Script

```bash
# List all available environments
node scripts/env-setup.js list

# Copy staging environment to local
node scripts/env-setup.js copy stg

# Validate an environment file
node scripts/env-setup.js validate stg

# Show environment file information
node scripts/env-setup.js info prod

# Show help
node scripts/env-setup.js help
```

### Manual Setup

1. Copy `.env.example` to `.env.local`
2. Modify variables as needed
3. Run `pnpm dev`

## Browser Configuration

The application supports multiple browsers for development:

- **Chrome** (default)
- **Firefox**
- **Edge**
- **Zen Browser**

Use the `BROWSER` environment variable or browser-specific scripts to change the default browser.

## Troubleshooting

### Environment File Not Found
```bash
# Check available files
node scripts/env-setup.js list

# Copy from existing environment
node scripts/env-setup.js copy stg
```

### Invalid Environment Variables
```bash
# Validate your environment file
node scripts/env-setup.js validate local
```

### Build Issues
```bash
# Clean and rebuild
pnpm clean
pnpm install
pnpm build:stg
```

## Best Practices

1. **Never commit `.env.local`** - It's in `.gitignore` for a reason
2. **Use staging for testing** - Test changes in staging before production
3. **Validate environments** - Use the validation script before deployment
4. **Keep secrets secure** - Don't expose sensitive data in environment files
5. **Document changes** - Update this guide when adding new variables

## Integration with CI/CD

Environment files can be used in CI/CD pipelines:

```yaml
# Example GitHub Actions
- name: Build for staging
  run: pnpm build:stg

- name: Build for production
  run: pnpm build:prod
```

## Support

For issues with environment setup:

1. Check this documentation
2. Run the validation script
3. Check the console for error messages
4. Verify all required variables are set
